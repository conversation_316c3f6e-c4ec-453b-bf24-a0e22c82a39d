This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: schemas.py, utils.py, model_interface.py, meta_cognitive_agent.py, main.py, example_retriever.py, dimension_calculator.py, config.py
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
config.py
dimension_calculator.py
example_retriever.py
main.py
meta_cognitive_agent.py
model_interface.py
schemas.py
utils.py
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="dimension_calculator.py">
"""
维度计算器 - 计算NER文本的多维度特征
"""

import re
import math
import logging
from typing import Dict, List, Any, Optional
from collections import Counter

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    spacy = None

logger = logging.getLogger(__name__)

class DimensionCalculator:
    """多维度特征计算器"""
    
    def __init__(self):
        """初始化计算器"""
        if SPACY_AVAILABLE:
            try:
                # 尝试加载spacy模型
                self.nlp = spacy.load("en_core_web_sm")
                self.spacy_available = True
                logger.info("✅ spaCy模型加载成功")
            except OSError as e:
                logger.warning(f"⚠️ spaCy模型未找到: {e}，使用简化计算方法")
                self.nlp = None
                self.spacy_available = False
        else:
            logger.warning("⚠️ spaCy未安装，使用简化计算方法")
            self.nlp = None
            self.spacy_available = False
    
    def calculate_entity_density(self, text: str, entities: List[Dict]) -> float:
        """
        计算实体密度：实体数量/句子数量
        
        Args:
            text: 原始文本
            entities: 实体列表，格式 [{'text': '...', 'label': '...', 'start': int, 'end': int}]
            
        Returns:
            float: 实体密度，范围0-10
        """
        if not text.strip():
            return 0.0
        
        # 计算句子数量
        sentences = self._count_sentences(text)
        if sentences == 0:
            return 0.0
        
        # 计算实体数量
        entity_count = len(entities) if entities else 0
        
        # 计算密度
        density = entity_count / sentences
        
        # 限制在0-10范围内
        return min(density, 10.0)
    
    def calculate_boundary_ambiguity(self, text: str, entities: List[Dict]) -> float:
        """
        计算实体边界模糊度
        
        Args:
            text: 原始文本
            entities: 实体列表
            
        Returns:
            float: 边界模糊度，范围0-1
        """
        if not entities:
            return 0.0
        
        ambiguity_score = 0.0
        total_entities = len(entities)
        
        for entity in entities:
            entity_text = entity.get('text', '')
            if not entity_text:
                continue
            
            score = 0.0
            
            # 检测复合词 (包含连字符、下划线)
            if '-' in entity_text or '_' in entity_text:
                score += 0.3
            
            # 检测多词实体
            if len(entity_text.split()) > 1:
                score += 0.2
            
            # 检测缩写 (全大写且长度2-5)
            if entity_text.isupper() and 2 <= len(entity_text) <= 5:
                score += 0.2
            
            # 检测数字混合
            if any(c.isdigit() for c in entity_text):
                score += 0.1
            
            # 检测特殊字符
            if any(c in entity_text for c in ['@', '#', '&', '.', ',']):
                score += 0.2
            
            # 检测括号或引号
            if any(c in entity_text for c in ['(', ')', '"', "'"]):
                score += 0.1
            
            ambiguity_score += min(score, 1.0)
        
        return min(ambiguity_score / total_entities, 1.0)
    
    def calculate_dependency_depth(self, text: str) -> int:
        """
        计算语法依赖深度
        
        Args:
            text: 原始文本
            
        Returns:
            int: 依赖深度，范围1-10
        """
        if not text.strip():
            return 1
        
        if self.spacy_available and self.nlp:
            return self._calculate_spacy_depth(text)
        else:
            return self._calculate_simple_depth(text)
    
    def _calculate_spacy_depth(self, text: str) -> int:
        """使用spaCy计算依赖深度"""
        try:
            doc = self.nlp(text)
            max_depth = 1
            
            for sent in doc.sents:
                for token in sent:
                    depth = self._get_token_depth(token)
                    max_depth = max(max_depth, depth)
            
            return min(max_depth, 10)
        except Exception as e:
            logger.warning(f"spaCy依赖分析失败: {e}")
            return self._calculate_simple_depth(text)
    
    def _get_token_depth(self, token, depth=1):
        """递归计算token的依赖深度"""
        if not list(token.children):
            return depth
        
        max_child_depth = depth
        for child in token.children:
            child_depth = self._get_token_depth(child, depth + 1)
            max_child_depth = max(max_child_depth, child_depth)
        
        return max_child_depth
    
    def _calculate_simple_depth(self, text: str) -> int:
        """简化的依赖深度计算（不使用spaCy）"""
        # 基于标点符号和句子结构的简单估计
        
        # 计算嵌套层次的指标
        depth_score = 1
        
        # 逗号数量（表示复杂句）
        comma_count = text.count(',')
        depth_score += min(comma_count * 0.3, 2)
        
        # 从句连接词
        subordinating_words = ['that', 'which', 'who', 'where', 'when', 'because', 'although', 'while', 'if']
        for word in subordinating_words:
            if f' {word} ' in text.lower():
                depth_score += 0.5
        
        # 括号嵌套
        paren_depth = 0
        max_paren_depth = 0
        for char in text:
            if char == '(':
                paren_depth += 1
                max_paren_depth = max(max_paren_depth, paren_depth)
            elif char == ')':
                paren_depth -= 1
        depth_score += max_paren_depth
        
        # 句子长度影响
        words = text.split()
        if len(words) > 20:
            depth_score += 1
        if len(words) > 40:
            depth_score += 1
        
        return min(int(depth_score), 10)
    
    def _count_sentences(self, text: str) -> int:
        """计算句子数量"""
        # 简单的句子分割
        sentences = re.split(r'[.!?]+', text.strip())
        # 过滤空句子
        sentences = [s.strip() for s in sentences if s.strip()]
        return max(len(sentences), 1)  # 至少1个句子
    
    def calculate_rhetorical_role(self, text: str) -> str:
        """
        计算修辞角色

        Args:
            text: 原始文本

        Returns:
            str: 修辞角色类型
        """
        # {{ AURA-X: Extend - 基于用户方案添加修辞角色分析. Approval: 寸止(ID:**********). }}
        # 简化的规则基础分析（可后续用LLM增强）
        text_lower = text.lower()

        # 检测陈述事实的模式
        if any(word in text_lower for word in ['said', 'reported', 'announced', 'stated', 'according to']):
            return '陈述事实'

        # 检测提出观点的模式
        if any(word in text_lower for word in ['believe', 'think', 'opinion', 'argue', 'suggest']):
            return '提出观点'

        # 检测举例说明的模式
        if any(word in text_lower for word in ['example', 'such as', 'for instance', 'including']):
            return '举例说明'

        # 检测反驳的模式
        if any(word in text_lower for word in ['however', 'but', 'although', 'despite', 'nevertheless']):
            return '进行反驳'

        # 默认为陈述事实
        return '陈述事实'

    def calculate_formality_level(self, text: str) -> float:
        """
        计算正式度

        Args:
            text: 原始文本

        Returns:
            float: 正式度评分，0-1范围
        """
        formality_score = 0.5  # 基础分数

        # 正式语言指标
        formal_indicators = ['furthermore', 'therefore', 'consequently', 'nevertheless', 'moreover']
        informal_indicators = ["don't", "can't", "won't", "it's", "that's", "i'm"]

        words = text.lower().split()
        total_words = len(words)

        if total_words == 0:
            return 0.5

        # 计算正式词汇比例
        formal_count = sum(1 for word in words if word in formal_indicators)
        informal_count = sum(1 for word in words if word in informal_indicators)

        # 调整分数
        formality_score += (formal_count / total_words) * 0.3
        formality_score -= (informal_count / total_words) * 0.3

        # 句子长度影响（长句通常更正式）
        avg_sentence_length = len(words) / max(self._count_sentences(text), 1)
        if avg_sentence_length > 15:
            formality_score += 0.1
        elif avg_sentence_length < 8:
            formality_score -= 0.1

        return max(0.0, min(1.0, formality_score))

    def calculate_information_density(self, text: str, entities: List[Dict]) -> float:
        """
        计算信息密度/惊奇度

        Args:
            text: 原始文本
            entities: 实体列表

        Returns:
            float: 信息密度评分，0-1范围
        """
        # 基于多个因素计算信息密度
        density_score = 0.0

        # 实体密度贡献
        entity_count = len(entities) if entities else 0
        words = text.split()
        if len(words) > 0:
            entity_ratio = entity_count / len(words)
            density_score += min(entity_ratio * 2, 0.3)  # 最多贡献0.3

        # 数字和特殊符号密度
        special_chars = sum(1 for char in text if char.isdigit() or char in '@#$%&*')
        if len(text) > 0:
            special_ratio = special_chars / len(text)
            density_score += min(special_ratio * 5, 0.2)  # 最多贡献0.2

        # 词汇多样性（TTR - Type-Token Ratio）
        unique_words = len(set(word.lower() for word in words))
        if len(words) > 0:
            ttr = unique_words / len(words)
            density_score += ttr * 0.3  # 最多贡献0.3

        # 句子复杂度贡献
        complexity = self.calculate_dependency_depth(text)
        density_score += min((complexity - 1) / 9 * 0.2, 0.2)  # 最多贡献0.2

        return max(0.0, min(1.0, density_score))

    def calculate_instruction_complexity(self, text: str, entities: List[Dict]) -> str:
        """
        计算指令复杂度

        Args:
            text: 原始文本
            entities: 实体列表

        Returns:
            str: 复杂度等级
        """
        complexity_score = 0

        # 实体数量影响
        entity_count = len(entities) if entities else 0
        if entity_count <= 2:
            complexity_score += 1
        elif entity_count <= 5:
            complexity_score += 2
        else:
            complexity_score += 3

        # 文本长度影响
        words = text.split()
        if len(words) <= 10:
            complexity_score += 1
        elif len(words) <= 30:
            complexity_score += 2
        else:
            complexity_score += 3

        # 语法复杂度影响
        dependency_depth = self.calculate_dependency_depth(text)
        if dependency_depth <= 2:
            complexity_score += 1
        elif dependency_depth <= 4:
            complexity_score += 2
        else:
            complexity_score += 3

        # 根据总分确定等级
        if complexity_score <= 4:
            return '低'
        elif complexity_score <= 7:
            return '中'
        else:
            return '高'

    def calculate_all_dimensions(self, text: str, entities: List[Dict]) -> Dict[str, Any]:
        """
        计算所有维度特征

        Args:
            text: 原始文本
            entities: 实体列表

        Returns:
            Dict: 包含所有维度的字典
        """
        try:
            # {{ AURA-X: Extend - 扩展维度计算，包含新的维度. Approval: 寸止(ID:**********). }}
            dimensions = {
                # 原有维度
                'entity_density': self.calculate_entity_density(text, entities),
                'boundary_ambiguity': self.calculate_boundary_ambiguity(text, entities),
                'dependency_depth': float(self.calculate_dependency_depth(text)),

                # 新增维度
                'rhetorical_role': self.calculate_rhetorical_role(text),
                'formality_level': self.calculate_formality_level(text),
                'information_density': self.calculate_information_density(text, entities),
                'instruction_complexity': self.calculate_instruction_complexity(text, entities)
            }

            logger.debug(f"计算维度完成: {dimensions}")
            return dimensions

        except Exception as e:
            logger.error(f"维度计算失败: {e}")
            # 返回默认值
            return {
                'entity_density': 1.0,
                'boundary_ambiguity': 0.5,
                'dependency_depth': 2.0,
                'rhetorical_role': '陈述事实',
                'formality_level': 0.5,
                'information_density': 0.5,
                'instruction_complexity': '中'
            }
    
    def estimate_dimensions_from_text(self, text: str) -> Dict[str, float]:
        """
        仅从文本估计维度（无实体信息）
        用于Stage 1阶段的LLM分析验证
        
        Args:
            text: 原始文本
            
        Returns:
            Dict: 估计的维度值
        """
        try:
            # 估计实体密度（基于大写词、专有名词模式）
            words = text.split()
            sentences = self._count_sentences(text)
            
            # 简单的实体估计：大写开头的词、数字、特殊模式
            potential_entities = 0
            for word in words:
                if word[0].isupper() and len(word) > 1:  # 大写开头
                    potential_entities += 1
                elif re.match(r'\d+', word):  # 数字
                    potential_entities += 1
                elif '@' in word or '#' in word:  # 特殊符号
                    potential_entities += 1
            
            estimated_density = min(potential_entities / sentences, 10.0)
            
            # 估计边界模糊度（基于复杂词汇模式）
            complex_patterns = 0
            total_words = len(words)
            
            for word in words:
                if '-' in word or '_' in word:
                    complex_patterns += 1
                elif word.isupper() and 2 <= len(word) <= 5:
                    complex_patterns += 1
                elif any(c.isdigit() for c in word):
                    complex_patterns += 1
            
            estimated_ambiguity = min(complex_patterns / max(total_words, 1), 1.0)
            
            # 依赖深度使用现有方法
            estimated_depth = float(self.calculate_dependency_depth(text))
            
            return {
                'entity_density': estimated_density,
                'boundary_ambiguity': estimated_ambiguity,
                'dependency_depth': estimated_depth
            }
            
        except Exception as e:
            logger.error(f"文本维度估计失败: {e}")
            return {
                'entity_density': 1.0,
                'boundary_ambiguity': 0.5,
                'dependency_depth': 2.0
            }
</file>

<file path="schemas.py">
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from enum import Enum

# ====== 🚀 元认知智能体Function Calling工具 ======

class DomainType(str, Enum):
    """领域类型枚举"""
    NEWS = "news"
    MEDICAL = "medical"
    FINANCIAL = "financial"
    ACADEMIC = "academic"
    SOCIAL = "social"
    GENERAL = "general"

class RetrieveNERExamplesTool(BaseModel):
    """🧠 多维度NER示例检索工具 - 元认知驱动的ICL示例检索"""

    # 基础描述（保持兼容）
    description: str = Field(
        description="简洁描述文本的关键特征，重点关注：文本类型(新闻/对话/学术等)、领域(医疗/金融/通用等)、语言风格(正式/口语等)、实体复杂度。20-30个词以内。"
    )

    # {{ AURA-X: Extend - 基于用户方案扩展多维度特征. Approval: 寸止(ID:**********). }}
    # 核心维度（现有，保持兼容）
    entity_density: Optional[float] = Field(
        description="实体密度：预估文本中实体数量与句子数量的比值，范围0-10。例如：低密度0-1，中密度2-3，高密度4+",
        default=None,
        ge=0.0,
        le=10.0
    )

    boundary_ambiguity: Optional[float] = Field(
        description="实体边界模糊度：预估实体边界不清晰的程度，0-1范围。0=边界清晰(如'Apple')，1=高度模糊(如'New York-based company')",
        default=None,
        ge=0.0,
        le=1.0
    )

    dependency_depth: Optional[int] = Field(
        description="语法依赖深度：预估句子中依赖关系的最大层次深度，1-10范围。简单句=1-2，复杂句=3-5，极复杂=6+",
        default=None,
        ge=1,
        le=10
    )

    # 新增维度（基于用户方案）
    rhetorical_role: Optional[str] = Field(
        description="修辞角色：文本的修辞功能，如'陈述事实'、'提出观点'、'举例说明'、'进行反驳'",
        default=None
    )

    formality_level: Optional[float] = Field(
        description="正式度：语言的正式程度，0-1范围。0=非正式/口语化，1=高度正式/学术化",
        default=None,
        ge=0.0,
        le=1.0
    )

    information_density: Optional[float] = Field(
        description="信息密度：文本的信息量/惊奇度，0-1范围。0=常见/可预测，1=罕见/高信息量",
        default=None,
        ge=0.0,
        le=1.0
    )

    instruction_complexity: Optional[str] = Field(
        description="指令复杂度：任务的复杂程度，'低'、'中'、'高'",
        default=None
    )

    # 扩展维度预留
    additional_features: Optional[Dict[str, Any]] = Field(
        description="额外的维度特征，用于未来扩展新的分析维度",
        default=None
    )

    k: int = Field(
        description="需要检索的示例数量，推荐2-3个以获得更好的覆盖度",
        default=3,
        ge=1,
        le=5
    )
</file>

<file path="example_retriever.py">
"""
示例检索器 - 简单高效的NER示例检索系统
遵循KISS原则：保持简单，避免过度设计
"""

import asyncio
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import json
import os
import time
import pickle
import hashlib
import threading
from collections import defaultdict

try:
    import faiss
    FAISS_AVAILABLE = True
    # 设置FAISS多线程数量为CPU核心数的一半，避免过度竞争
    cpu_count = os.cpu_count() or 4
    faiss_threads = max(1, cpu_count // 2)
    try:
        faiss.omp_set_num_threads(faiss_threads)
        logging.info(f"✅ FAISS多线程已启用: {faiss_threads} 线程")
    except AttributeError:
        # 某些FAISS版本可能没有omp_set_num_threads
        logging.info("✅ FAISS已加载，使用默认线程配置")
except ImportError:
    FAISS_AVAILABLE = False
    logging.warning("FAISS not available, falling back to simple vector search")


from model_interface import model_service
from config import CONFIG, get_current_dataset_path, DIMENSION_REGISTRY
from dimension_calculator import DimensionCalculator

logger = logging.getLogger(__name__)


class BatchProcessor:
    """{{ AURA-X: Fix - 重构为真正并发的批处理器. Approval: 寸止(ID:1754056919). }}
    高性能并发批处理器 - 支持真正并发"""

    def __init__(self, batch_size: int = 16, timeout: float = 0.1):
        self.batch_size = batch_size
        self.timeout = timeout  # 大幅减少等待时间
        self.request_futures = {}
        self.lock = asyncio.Lock()
        # {{ AURA-X: Fix - 移除串行处理标志，支持并发. Approval: 寸止(ID:1754056919). }}
        self.semaphore = asyncio.Semaphore(10)  # 限制并发数避免过载

    async def add_request(self, request_id: str, description: str) -> List[float]:
        """{{ AURA-X: Fix - 重构为并发处理. Approval: 寸止(ID:1754056919). }}
        并发处理嵌入请求 - 真正的并发版本"""

        # {{ AURA-X: Fix - 使用信号量控制并发，避免过载. Approval: 寸止(ID:1754056919). }}
        async with self.semaphore:
            try:
                # 直接调用API，不再使用复杂的批处理逻辑
                logger.debug(f"🔍 直接处理嵌入请求: {request_id}")
                embeddings = await asyncio.wait_for(
                    model_service.get_embeddings_async([description]),
                    timeout=60.0  # 单个请求60秒超时
                )

                if embeddings and len(embeddings) > 0:
                    logger.debug(f"✅ 嵌入请求完成: {request_id}")
                    return embeddings[0]  # 返回第一个（也是唯一的）嵌入
                else:
                    logger.warning(f"⚠️ 嵌入请求返回空结果: {request_id}")
                    return []

            except asyncio.TimeoutError:
                logger.warning(f"🚨 嵌入请求超时: {request_id}")
                raise
            except Exception as e:
                logger.error(f"🚨 嵌入请求失败: {request_id}, 错误: {e}")
                return []

    # {{ AURA-X: Fix - 移除批处理方法，改为直接并发处理. Approval: 寸止(ID:1754056919). }}
    # 批处理方法已移除，现在使用直接并发处理

    # {{ AURA-X: Fix - 移除批处理辅助方法，不再需要. Approval: 寸止(ID:1754056919). }}
    # 批处理辅助方法已移除，现在使用直接并发处理




class FAISSVectorStore:
    """FAISS高性能向量存储 - 并发安全版本"""

    def __init__(self):
        self.examples = []
        self.metadata = []
        self.embeddings = []
        self.index = None
        self.dimension = None
        self.initialized = False
        # {{ AURA-X: Fix - 避免锁初始化的竞态条件. Approval: 寸止(ID:1738230400). }}
        # 使用threading.Lock来保护asyncio.Lock的初始化
        import threading
        self._init_lock = threading.Lock()
        self._search_lock = None

    def add_examples(self, examples: List[Dict], embeddings: List[List[float]], metadata: List[Dict]):
        """添加示例和向量"""
        self.examples.extend(examples)
        self.metadata.extend(metadata)
        self.embeddings.extend(embeddings)

        if not self.dimension and embeddings:
            self.dimension = len(embeddings[0])

        self._build_index()

    def _build_index(self):
        """构建FAISS索引"""
        if not self.embeddings or not FAISS_AVAILABLE:
            return

        try:
            # 转换为numpy数组
            embeddings_array = np.array(self.embeddings, dtype=np.float32)

            # 创建FAISS索引
            if self.dimension:
                # {{ AURA-X: Fix - 强制使用简单索引避免IVF卡死. Approval: 寸止(ID:1754055919). }}
                # 暂时强制使用简单索引，避免IVF索引卡死问题
                logger.info(f"🔍 使用IndexFlatIP索引: {len(self.embeddings)}个向量")
                self.index = faiss.IndexFlatIP(self.dimension)
                # IndexFlatIP不需要训练和设置nprobe，它是精确搜索

                self.index.add(embeddings_array)
                self.initialized = True
                logger.info(f"✅ FAISS索引构建完成: {len(self.embeddings)}个向量, 维度{self.dimension}, 多线程已启用")

        except Exception as e:
            logger.error(f"FAISS索引构建失败: {e}")
            self.initialized = False

    async def search(self, query_embedding: List[float], top_k: int = 20) -> List[Tuple[int, float]]:
        """{{ AURA-X: Fix - 简化FAISS检索，移除复杂锁机制. Approval: 寸止(ID:1754055919). }}
        FAISS向量检索 - 简化版本"""
        if not self.initialized or not self.index:
            logger.warning("FAISS未初始化或索引不存在")
            return []

        try:
            logger.info(f"🔍 执行FAISS检索: top_k={top_k}, 索引大小={len(self.embeddings)}")

            # {{ AURA-X: Fix - 添加详细调试信息. Approval: 寸止(ID:1754055919). }}
            def _sync_search():
                logger.info(f"🔍 准备查询数组: 嵌入长度={len(query_embedding)}")
                query_array = np.array([query_embedding], dtype=np.float32)
                logger.info(f"🔍 查询数组形状: {query_array.shape}")
                logger.info(f"🔍 索引类型: {type(self.index)}")
                logger.info(f"🔍 开始调用index.search...")
                result = self.index.search(query_array, min(top_k, len(self.embeddings)))
                logger.info(f"🔍 index.search调用完成")
                return result

            # 在线程池中执行同步的FAISS检索
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                logger.info(f"🔍 提交到线程池执行...")
                scores, indices = await asyncio.get_event_loop().run_in_executor(executor, _sync_search)
                logger.info(f"🔍 线程池执行完成")

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx >= 0:  # FAISS返回-1表示无效结果
                    results.append((int(idx), float(score)))

            logger.info(f"🔍 FAISS检索成功: 返回{len(results)}个结果")
            return results

        except Exception as e:
            logger.error(f"FAISS检索失败: {e}")
            import traceback
            logger.error(f"FAISS检索失败堆栈: {traceback.format_exc()}")
            return []


class ExampleRetriever:
    """高性能NER任务示例检索器 - 支持FAISS+批处理"""

    def __init__(self):
        self.config = CONFIG.get('retrieval_config', {})
        self.model_service = model_service

        # 选择向量存储后端
        if not FAISS_AVAILABLE:
            raise ImportError("FAISS is not available, which is required for the vector store. Please install it using 'pip install faiss-cpu' or 'pip install faiss-gpu'.")
        self.vector_store = FAISSVectorStore()

        # 统一批处理参数，从CONFIG获取配置
        embedding_batch_size = min(CONFIG.get('batch_size', 32), 16)  # 减小批次大小
        batch_timeout = min(CONFIG.get('batch_delay', 1.0) * 0.3, 0.3)  # 减小超时时间
        self.batch_processor = BatchProcessor(batch_size=embedding_batch_size, timeout=batch_timeout)
        self.initialized = False

        # 初始化维度计算器
        self.dimension_calculator = DimensionCalculator()

        logger.info("示例检索器初始化完成")




    async def _load_pkl_cache(self, pkl_file: str, data_path: str) -> bool:
        """加载pkl缓存"""
        try:
            # 在线程池中执行pickle加载，避免阻塞事件循环
            def _load_pickle():
                with open(pkl_file, 'rb') as f:
                    return pickle.load(f)

            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                cached_data = await asyncio.get_event_loop().run_in_executor(executor, _load_pickle)

            # 验证缓存
            if not self._validate_cache(cached_data, data_path):
                logger.warning("缓存验证失败，重新生成...")
                return False

            # {{ AURA-X: Fix - 检查缓存版本和metadata完整性. Approval: 寸止(ID:1738230402). }}
            cache_version = cached_data.get('version', '1.0')
            if cache_version < '1.1':
                logger.warning(f"缓存版本过旧 ({cache_version})，缺少metadata，重新生成...")
                return False

            examples = cached_data['examples']
            embeddings = cached_data['embeddings']
            metadata = cached_data.get('metadata', None)

            # 严格检查metadata完整性
            if metadata is None or len(metadata) != len(examples):
                logger.warning(f"metadata不完整: 预期{len(examples)}个，实际{len(metadata) if metadata else 0}个，重新生成...")
                return False

            # 验证metadata结构
            for i, meta in enumerate(metadata):
                if not isinstance(meta, dict) or not meta:
                    logger.warning(f"metadata[{i}]为空或格式错误，重新生成...")
                    return False
                # 检查必要的维度字段
                required_dims = ['entity_density', 'boundary_ambiguity', 'dependency_depth']
                if not all(dim in meta for dim in required_dims):
                    logger.warning(f"metadata[{i}]缺少必要维度字段，重新生成...")
                    return False

            self.vector_store.add_examples(examples, embeddings, metadata)

            logger.info(f"从pkl缓存加载 {len(examples)} 个示例，包含完整metadata")
            self.initialized = True
            return True

        except Exception as e:
            logger.warning(f"加载pkl缓存失败: {e}")
            return False

    async def _migrate_json_to_pkl(self, json_file: str, pkl_file: str, data_path: str) -> bool:
        """将JSON缓存迁移到pkl格式"""
        try:
            # 在线程池中执行文件操作，避免阻塞事件循环
            def _load_json():
                with open(json_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                json_data = await asyncio.get_event_loop().run_in_executor(executor, _load_json)

            # {{ AURA-X: Fix - JSON迁移时重新计算metadata. Approval: 寸止(ID:1738230402). }}
            # 旧JSON缓存没有metadata，需要重新计算
            logger.info("🧠 JSON缓存缺少metadata，重新计算维度特征...")

            examples = json_data['examples']
            metadata = []
            for i, example in enumerate(examples):
                text = example.get('text', '')

                # 转换label格式为entities格式
                entities = []
                labels = example.get('label', {})
                for entity_type, entity_list in labels.items():
                    for entity_text in entity_list:
                        start_pos = text.find(entity_text)
                        if start_pos != -1:
                            entities.append({
                                'text': entity_text,
                                'label': entity_type,
                                'start': start_pos,
                                'end': start_pos + len(entity_text)
                            })

                # 计算维度特征
                dimensions = self.dimension_calculator.calculate_all_dimensions(text, entities)
                metadata.append(dimensions)

                if (i + 1) % 100 == 0:
                    logger.info(f"🧠 JSON迁移: 已计算 {i + 1}/{len(examples)} 个样本的维度特征")

            # 添加版本信息
            pkl_data = {
                'examples': examples,
                'embeddings': json_data['embeddings'],
                'metadata': metadata,  # 包含重新计算的metadata
                'dataset_path': data_path,
                'created_at': json_data.get('created_at', time.time()),
                'version': '1.1',  # 升级版本
                'dataset_hash': self._get_dataset_hash(data_path)
            }

            # 在线程池中保存pkl格式
            def _save_pickle():
                with open(pkl_file, 'wb') as f:
                    pickle.dump(pkl_data, f)
            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                await asyncio.get_event_loop().run_in_executor(executor, _save_pickle)

            # 删除旧的JSON缓存
            os.remove(json_file)

            # 加载到向量存储
            # {{ AURA-X: Fix - 使用重新计算的完整metadata. Approval: 寸止(ID:1738230402). }}
            self.vector_store.add_examples(pkl_data['examples'], pkl_data['embeddings'], pkl_data['metadata'])
            logger.info(f"成功迁移并加载 {len(pkl_data['examples'])} 个示例，包含完整metadata")
            self.initialized = True
            return True

        except Exception as e:
            logger.warning(f"迁移JSON缓存失败: {e}")
            return False

    def _validate_cache(self, cached_data: Dict[str, Any], data_path: str) -> bool:
        """验证缓存有效性"""
        try:
            # {{ AURA-X: Fix - 增强缓存验证，检查版本和metadata. Approval: 寸止(ID:1738230402). }}
            # 检查必需字段
            required_fields = ['examples', 'embeddings', 'dataset_path']
            if not all(field in cached_data for field in required_fields):
                return False

            # 检查版本和metadata
            cache_version = cached_data.get('version', '1.0')
            if cache_version < '1.1':
                logger.info(f"缓存版本过旧: {cache_version}，需要重新生成")
                return False

            # 检查metadata字段
            if 'metadata' not in cached_data:
                logger.info("缓存缺少metadata字段，需要重新生成")
                return False

            # 检查数据集是否已更改
            if cached_data['dataset_path'] != data_path:
                return False

            # 检查数据集文件哈希（如果存在）
            if 'dataset_hash' in cached_data:
                current_hash = self._get_dataset_hash(data_path)
                if cached_data['dataset_hash'] != current_hash:
                    logger.info("数据集文件已更改，缓存失效")
                    return False

            # 检查数据一致性
            examples = cached_data['examples']
            embeddings = cached_data['embeddings']
            metadata = cached_data['metadata']

            if len(examples) != len(embeddings) or len(examples) != len(metadata):
                logger.info(f"数据长度不匹配: examples={len(examples)}, embeddings={len(embeddings)}, metadata={len(metadata)}")
                return False

            return True

        except Exception:
            return False

    def _get_dataset_hash(self, data_path: str) -> str:
        """获取数据集文件哈希"""
        try:
            with open(data_path, 'rb') as f:
                content = f.read()
            return hashlib.md5(content).hexdigest()
        except Exception:
            return ""

    async def _generate_embeddings_in_batches(self, texts: List[str], batch_size: int = 100, max_concurrent: int = 10) -> List[List[float]]:
        """🚀 并发批量生成嵌入向量，大幅提升效率"""
        total_batches = (len(texts) + batch_size - 1) // batch_size

        logger.info(f"🚀 开始并发生成嵌入向量: {len(texts)} 个文本, {total_batches} 个批次, 最大并发: {max_concurrent} (优化版)")

        # 创建批次任务
        batch_tasks = []
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_num = i // batch_size + 1
            batch_tasks.append(self._process_single_batch(batch_texts, batch_num, total_batches))

        # 使用信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_with_semaphore(task):
            async with semaphore:
                return await task

        # 并发执行所有批次
        try:
            batch_results = await asyncio.gather(*[process_with_semaphore(task) for task in batch_tasks])

            # 合并结果
            all_embeddings = []
            for batch_embeddings in batch_results:
                if batch_embeddings:
                    all_embeddings.extend(batch_embeddings)
                else:
                    logger.error("某个批次嵌入生成失败")
                    return []

            logger.info(f"✅ 并发嵌入生成完成: {len(all_embeddings)} 个向量")
            return all_embeddings

        except Exception as e:
            logger.error(f"❌ 并发嵌入生成失败: {e}")
            return []

    async def _process_single_batch(self, batch_texts: List[str], batch_num: int, total_batches: int) -> List[List[float]]:
        """处理单个批次的嵌入生成"""
        try:
            logger.info(f"🔄 处理批次 {batch_num}/{total_batches}: {len(batch_texts)} 个文本")
            batch_embeddings = await self.model_service.get_embeddings_async(batch_texts)
            if batch_embeddings:
                logger.info(f"✅ 批次 {batch_num} 完成")
                return batch_embeddings
            else:
                logger.error(f"❌ 批次 {batch_num} 嵌入生成失败")
                return []
        except Exception as e:
            logger.error(f"❌ 批次 {batch_num} 处理失败: {e}")
            return []

    async def _generate_and_cache_vectors(self, data_path: str, pkl_cache_file: str) -> bool:
        """生成向量并保存到pkl缓存"""
        try:
            # 在线程池中加载数据集，避免阻塞事件循环
            def _load_dataset():
                with open(data_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                examples = await asyncio.get_event_loop().run_in_executor(executor, _load_dataset)

            # 批量生成语义向量（避免API超时）
            texts = [example.get('text', '') for example in examples]
            embeddings = await self._generate_embeddings_in_batches(texts, batch_size=50)

            if not embeddings or len(embeddings) != len(examples):
                logger.error(f"嵌入生成失败: 预期 {len(examples)}, 实际 {len(embeddings) if embeddings else 0}")
                return False

            # 计算维度特征并添加到metadata
            logger.info("🧠 开始计算训练数据的维度特征...")
            metadata = []
            for i, example in enumerate(examples):
                text = example.get('text', '')

                # 转换label格式为entities格式
                entities = []
                labels = example.get('label', {})
                for entity_type, entity_list in labels.items():
                    for entity_text in entity_list:
                        # 在文本中查找实体位置
                        start_pos = text.find(entity_text)
                        if start_pos != -1:
                            entities.append({
                                'text': entity_text,
                                'label': entity_type,
                                'start': start_pos,
                                'end': start_pos + len(entity_text)
                            })

                # 计算维度特征
                dimensions = self.dimension_calculator.calculate_all_dimensions(text, entities)
                metadata.append(dimensions)

                if (i + 1) % 100 == 0:
                    logger.info(f"🧠 已计算 {i + 1}/{len(examples)} 个样本的维度特征")

            logger.info(f"✅ 维度特征计算完成: {len(metadata)} 个样本")

            # 添加到向量存储
            self.vector_store.add_examples(examples, embeddings, metadata)

            # 保存到pkl缓存
            # {{ AURA-X: Fix - 添加metadata字段到缓存数据. Approval: 寸止(ID:1738230402). }}
            cache_data = {
                'examples': examples,
                'embeddings': embeddings,
                'metadata': metadata,  # 关键修复：保存维度特征metadata
                'dataset_path': data_path,
                'created_at': time.time(),
                'version': '1.1',  # 版本升级，标识包含metadata
                'dataset_hash': self._get_dataset_hash(data_path)
            }

            # 在线程池中保存缓存，避免阻塞事件循环
            def _save_cache():
                with open(pkl_cache_file, 'wb') as f:
                    pickle.dump(cache_data, f)

            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                await asyncio.get_event_loop().run_in_executor(executor, _save_cache)

            logger.info(f"向量存储初始化完成，生成并缓存 {len(examples)} 个示例")
            self.initialized = True
            return True

        except Exception as e:
            logger.error(f"生成向量缓存失败: {e}")
            return False

    async def initialize_vector_store(self, data_path: Optional[str] = None) -> bool:
        """🔍 KISS原则：检测有无向量库，有则继续，没有就生成"""
        try:
            # 获取数据集路径
            if data_path is None:
                data_path = get_current_dataset_path()

            if not os.path.exists(data_path):
                logger.warning(f"数据集文件不存在: {data_path}")
                return False

            # 简单的缓存检测
            from config import get_dataset_cache_dir
            cache_dir = get_dataset_cache_dir()

            file_name = os.path.basename(data_path).replace('.json', '')
            pkl_cache_file = os.path.join(cache_dir, f"{file_name}_vectors.pkl")

            # 检测向量库是否存在
            if os.path.exists(pkl_cache_file):
                logger.info("🔍 检测到现有向量库，正在加载...")
                if await self._load_pkl_cache(pkl_cache_file, data_path):
                    logger.info("✅ 向量库加载成功")
                    return True
                else:
                    logger.warning("⚠️ 向量库加载失败，重新生成...")

            # 向量库不存在或损坏，生成新的
            logger.info("🚀 向量库不存在，开始生成...")
            success = await self._generate_and_cache_vectors(data_path, pkl_cache_file)
            if success:
                logger.info("✅ 向量库生成完成")
            return success

        except Exception as e:
            logger.error(f"向量库初始化失败: {e}")
            return False




    async def _generate_query_embedding(self, query: str) -> List[float]:
        """生成查询嵌入 - 使用批处理器优化"""
        try:
            # {{ AURA-X: Fix - 添加调试信息. Approval: 寸止(ID:1754055919). }}
            logger.info(f"🔍 开始生成查询嵌入: {query[:50]}...")
            request_id = f"query_{hash(query)}_{time.time()}"
            logger.info(f"🔍 请求ID: {request_id}")
            embedding = await self.batch_processor.add_request(request_id, query)
            logger.info(f"🔍 嵌入生成完成: 长度={len(embedding) if embedding else 0}")
            if not embedding:
                logger.warning(f"🚨 查询嵌入为空: query='{query[:50]}...', request_id={request_id}")
            return embedding if embedding else []
        except Exception as e:
            logger.error(f"🚨 生成查询嵌入失败: query='{query[:50]}...', 错误: {e}")
            import traceback
            logger.error(f"🚨 查询嵌入失败详细堆栈: {traceback.format_exc()}")
            return []

    async def retrieve_with_dimensions(
        self,
        query_text: str,
        query_dimensions: Optional[Dict[str, Any]] = None,
        k: int = 3
    ) -> List[Dict[str, Any]]:
        """
        多维度检索方法 - 结合文本相似度和维度相似度

        Args:
            query_text: 查询文本描述
            query_dimensions: 多维度特征
            k: 需要的示例数量

        Returns:
            List[Dict]: k个示例
        """
        # 检查多维度配置
        multi_config = CONFIG.get('multi_dimensional_config', {})
        if not multi_config.get('enabled', False):
            logger.info("🔮 多维度检索未启用，使用纯文本检索")
            return await self.simple_retrieve(query_text, k)

        # 如果没有提供查询维度，从文本估算
        if not query_dimensions:
            logger.info("🧠 从查询文本估算维度特征")
            query_dimensions = self.dimension_calculator.estimate_dimensions_from_text(query_text)

        logger.info(f"🔮 多维度检索: {query_dimensions}")

        # 获取更多候选结果用于重排序
        candidate_count = max(k * 4, 20)  # 获取4倍候选数量
        candidates = await self.simple_retrieve(query_text, candidate_count)

        if not candidates:
            return []

        # {{ AURA-X: Extend - 使用RRF融合进行多维度检索. Approval: 寸止(ID:**********). }}
        # 准备多维度搜索
        text_ranking = [(candidate.get('index', -1), candidate.get('score', 0.0)) for candidate in candidates]
        dimension_rankings = self._parallel_dimension_search(query_dimensions, candidate_count)

        # 合并所有排名列表
        all_rankings = [text_ranking] + dimension_rankings

        # 计算RRF分数
        rrf_scores = self._calculate_rrf_score(all_rankings)

        # 重新评分候选样本
        scored_candidates = []
        weights = multi_config.get('dimension_weights', {})

        for candidate in candidates:
            candidate_idx = candidate.get('index', -1)

            # 获取RRF分数
            rrf_score = rrf_scores.get(candidate_idx, 0.0)

            # 获取文本相似度
            text_similarity = candidate.get('score', 0.0)

            # 计算最终分数（RRF + 文本相似度的加权组合）
            final_score = (
                weights.get('text_similarity', 0.6) * text_similarity +
                weights.get('rrf_fusion', 0.4) * rrf_score
            )

            candidate['multi_dim_score'] = final_score
            candidate['rrf_score'] = rrf_score

            # 添加维度相似度信息（用于调试）
            if candidate_idx >= 0 and candidate_idx < len(self.vector_store.metadata):
                candidate_dimensions = self.vector_store.metadata[candidate_idx]
                if candidate_dimensions and isinstance(candidate_dimensions, dict):
                    dim_similarity = self._calculate_dimension_similarity(query_dimensions, candidate_dimensions)
                    candidate['dimension_similarity'] = dim_similarity
                    logger.debug(f"🔮 样本{candidate_idx}: 文本={text_similarity:.3f}, RRF={rrf_score:.3f}, 最终={final_score:.3f}")
                else:
                    logger.debug(f"⚠️ 样本{candidate_idx}: metadata为空，RRF={rrf_score:.3f}")

            scored_candidates.append(candidate)

        # 按最终评分重新排序
        scored_candidates.sort(key=lambda x: x['multi_dim_score'], reverse=True)

        # 返回前k个结果
        result = scored_candidates[:k]

        # {{ AURA-X: Fix - 增强日志信息，显示多维度检索效果. Approval: 寸止(ID:1738230402). }}
        valid_metadata_count = sum(1 for c in scored_candidates if c.get('dimension_similarity'))
        logger.info(f"🎯 多维度检索完成: 返回{len(result)}个示例，{valid_metadata_count}/{len(scored_candidates)}个样本有有效metadata")

        if valid_metadata_count == 0:
            logger.warning("⚠️ 所有样本的metadata都为空，多维度检索退化为纯文本检索")

        return result

    def _calculate_dimension_similarity(self, query_dims: Dict[str, Any], candidate_dims: Dict[str, Any]) -> Dict[str, float]:
        """
        计算多维度相似度

        Args:
            query_dims: 查询的维度特征
            candidate_dims: 候选样本的维度特征

        Returns:
            Dict[str, float]: 各维度的相似度评分
        """
        similarities = {}

        # {{ AURA-X: Extend - 扩展维度相似度计算，支持新维度. Approval: 寸止(ID:**********). }}
        # 数值维度相似度计算
        numerical_dims = {
            'entity_density': 1.0,      # sigma=1.0
            'boundary_ambiguity': 0.3,  # sigma=0.3
            'dependency_depth': 2.0,    # sigma=2.0
            'formality_level': 0.2,     # sigma=0.2
            'information_density': 0.3   # sigma=0.3
        }

        for dim, sigma in numerical_dims.items():
            if dim in query_dims and dim in candidate_dims:
                diff = abs(float(query_dims[dim]) - float(candidate_dims[dim]))
                # 使用高斯函数计算相似度
                similarities[dim] = np.exp(-(diff ** 2) / (2 * sigma ** 2))
            else:
                similarities[dim] = 0.0

        # 类别维度相似度计算
        categorical_dims = ['rhetorical_role', 'instruction_complexity']

        for dim in categorical_dims:
            if dim in query_dims and dim in candidate_dims:
                # 完全匹配得1分，不匹配得0分
                similarities[dim] = 1.0 if query_dims[dim] == candidate_dims[dim] else 0.0
            else:
                similarities[dim] = 0.0

        return similarities

    def _calculate_rrf_score(self, rankings: List[List[tuple]], k: int = 60) -> Dict[int, float]:
        """
        计算倒数排名融合(RRF)分数

        Args:
            rankings: 多个排名列表，每个列表包含(doc_id, score)元组
            k: RRF常数，通常为60

        Returns:
            Dict[int, float]: 文档ID到RRF分数的映射
        """
        # {{ AURA-X: Extend - 实现RRF融合算法. Approval: 寸止(ID:**********). }}
        rrf_scores = {}

        for ranking in rankings:
            for rank, (doc_id, _) in enumerate(ranking):
                if doc_id not in rrf_scores:
                    rrf_scores[doc_id] = 0.0
                # RRF公式: 1 / (k + rank)
                rrf_scores[doc_id] += 1.0 / (k + rank + 1)  # rank从0开始，所以+1

        return rrf_scores

    def _parallel_dimension_search(self, query_dims: Dict[str, Any], k: int) -> List[List[tuple]]:
        """
        【优化版】并行多维度搜索 - 使用NumPy向量化计算
        """
        if not self.vector_store.metadata:
            logger.warning("⚠️ 向量库metadata为空，无法进行多维度搜索")
            return []

        # 1. 数据准备：将metadata列表转换为NumPy数组，只做一次！
        all_metadata = self.vector_store.metadata
        num_candidates = len(all_metadata)

        # 从维度注册表获取维度配置
        numerical_dims = {}
        categorical_dims = {}

        # 如果维度注册表可用，使用注册表配置
        if DIMENSION_REGISTRY:
            for dim_name, dim_config in DIMENSION_REGISTRY.items():
                if dim_config['type'] == 'numerical':
                    numerical_dims[dim_name] = dim_config['similarity_params']
                elif dim_config['type'] == 'categorical':
                    categorical_dims[dim_name] = dim_config['similarity_params']
        else:
            # 回退到硬编码配置
            numerical_dims = {
                'entity_density': {'sigma': 1.0, 'weight': 0.2},
                'boundary_ambiguity': {'sigma': 0.3, 'weight': 0.2},
                'dependency_depth': {'sigma': 2.0, 'weight': 0.2},
                'formality_level': {'sigma': 0.2, 'weight': 0.1},
                'information_density': {'sigma': 0.3, 'weight': 0.1}
            }
            categorical_dims = {
                'rhetorical_role': {'weight': 0.1},
                'instruction_complexity': {'weight': 0.1}
            }

        # 初始化总分数组
        total_scores = np.zeros(num_candidates)
        total_weights = np.zeros(num_candidates)

        # 2. 高效计算：向量化处理
        # 处理数值型维度
        for dim, params in numerical_dims.items():
            if dim in query_dims:
                # 从所有候选中提取该维度的值，形成一个向量
                candidate_values = np.array([meta.get(dim, 0.0) for meta in all_metadata], dtype=np.float32)
                query_value = float(query_dims[dim])

                # NumPy向量化计算高斯相似度
                diff = np.abs(candidate_values - query_value)
                similarity_scores = np.exp(-(diff ** 2) / (2 * params['sigma'] ** 2))

                # 累加加权分数
                total_scores += similarity_scores * params['weight']
                total_weights += params['weight']

        # 处理类别型维度
        for dim, params in categorical_dims.items():
            if dim in query_dims:
                # 提取所有候选的类别值
                candidate_values = np.array([meta.get(dim, '') for meta in all_metadata])
                query_value = query_dims[dim]

                # NumPy向量化比较，得到一个布尔数组，然后转为0/1
                similarity_scores = (candidate_values == query_value).astype(np.float32)

                # 累加加权分数
                total_scores += similarity_scores * params['weight']
                total_weights += params['weight']

        # 3. 计算最终的加权平均分，避免除以零
        final_scores = np.divide(total_scores, total_weights, out=np.zeros_like(total_scores), where=total_weights!=0)

        # 4. 排序并返回结果
        # 使用np.argsort获取排序后的索引，[-k:]取最大的k个，[::-1]反转顺序
        top_k_indices = np.argsort(final_scores)[-k:][::-1]

        rankings = [
            (int(idx), float(final_scores[idx])) for idx in top_k_indices
        ]

        # 因为我们只有一个综合排名，所以把它包在列表里返回
        return [rankings]

    async def simple_retrieve(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        高性能检索方法 - 使用FAISS+批处理优化

        Args:
            description: LLM生成的检索描述
            k: 需要的示例数量

        Returns:
            List[Dict]: k个示例
        """
        try:
            if not self.initialized:
                logger.warning("⚠️ 向量存储未初始化")
                return []

            logger.info(f"🔍 高性能检索: {description[:50]}..., k={k}")

            # 生成查询嵌入（使用批处理器）
            query_embedding = await self._generate_query_embedding(description)
            if not query_embedding:
                logger.warning("查询嵌入生成失败")
                return []

            # 使用FAISS进行高速检索
            if isinstance(self.vector_store, FAISSVectorStore) and self.vector_store.initialized:
                # {{ AURA-X: Fix - 添加FAISS检索调试信息. Approval: 寸止(ID:1754055919). }}
                logger.info(f"🔍 开始FAISS检索: k={k}")
                search_results = await self.vector_store.search(query_embedding, top_k=k)
                logger.info(f"🔍 FAISS检索完成: 结果数={len(search_results)}")

                results = []
                for idx, score in search_results:
                    if idx < len(self.vector_store.examples):
                        example = self.vector_store.examples[idx]
                        results.append({
                            'text': example.get('text', ''),
                            'label': example.get('label', {}),
                            'similarity_score': float(score),
                            'index': idx  # 添加索引字段，用于多维度检索
                        })

                logger.info(f"✅ FAISS检索完成: 返回 {len(results)} 个示例")
                return results
            else:
                logger.warning("⚠️ FAISS向量存储未初始化")
                return []

        except Exception as e:
            logger.error(f"检索失败: {e}")
            return []




# 全局单例
example_retriever = ExampleRetriever()
</file>

<file path="meta_cognitive_agent.py">
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 元认知智能体 - 基于单一超级Prompt的NER系统
核心理念：LLM一次性完成思考、决策和执行
遵循KISS原则：简单、高效、优雅
"""

import logging
from typing import Dict, List, Any, Type
from pydantic import BaseModel

from config import get_current_dataset_info
from model_interface import model_service
from schemas import RetrieveNERExamplesTool
from utils import parse_tool_arguments, robust_json_parse_ner

logger = logging.getLogger(__name__)


class MetaCognitiveAgent:
    """🧠 元认知智能体 - 单一超级Prompt架构的核心引擎"""
    
    def __init__(self, example_retriever=None):
        """
        初始化元认知智能体

        Args:
            example_retriever: 预初始化的示例检索器
        """
        if example_retriever is None:
            from example_retriever import example_retriever as default_retriever
            self.example_retriever = default_retriever
        else:
            self.example_retriever = example_retriever
        self.model_service = model_service
        self._initialization_started = False
        
    def _get_current_entity_types(self) -> List[str]:
        """获取当前数据集的实体类型"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('labels', ['person', 'organization', 'location'])
    
    def _get_current_label_prompt(self) -> str:
        """获取当前数据集的标签提示"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('label_prompt', '')

    def _build_stage1_prompt(self, text: str) -> str:
        """
        {{ AURA-X: Optimize - 强化JSON格式要求. Approval: 寸止(ID:1738230400). }}
        构建Stage 1的prompt - 让LLM分析文本并生成检索请求
        """
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        # {{ AURA-X: Extend - 改进prompt支持扩展的多维度分析. Approval: 寸止(ID:**********). }}
        return f"""Analyze this text and retrieve relevant NER examples using multi-dimensional meta-cognitive analysis.

Target entity types: {entity_types_str}
Text to analyze: "{text}"

Use the retrieve_ner_examples tool to find the most helpful examples. Perform comprehensive analysis across these dimensions:

**Core Dimensions:**
1. **Description**: Concise text characteristics (20-30 words) - domain, style, entity patterns
2. **Entity Density**: Entities per sentence (0-10 scale: 0-1=sparse, 2-3=medium, 4+=dense)
3. **Boundary Ambiguity**: Entity boundary clarity (0-1 scale: 0=clear 'Apple', 1=ambiguous 'New York-based')
4. **Dependency Depth**: Syntactic complexity (1-10 scale: 1-2=simple, 3-5=complex, 6+=very complex)

**Extended Dimensions:**
5. **Rhetorical Role**: Text function ('陈述事实', '提出观点', '举例说明', '进行反驳')
6. **Formality Level**: Language formality (0-1 scale: 0=informal/colloquial, 1=formal/academic)
7. **Information Density**: Information richness/surprisal (0-1 scale: 0=common/predictable, 1=rare/high-info)
8. **Instruction Complexity**: Task difficulty ('低', '中', '高')

Think meta-cognitively: "What kind of examples would best help me understand how to extract entities from THIS specific text?" Focus on finding examples that match the text's characteristics and challenges."""

    async def _ensure_initialized(self):
        """确保示例检索器已初始化"""
        if not self._initialization_started and self.example_retriever and not self.example_retriever.initialized:
            self._initialization_started = True
            logger.info("🔧 自动初始化示例检索器...")
            await self.example_retriever.initialize_vector_store()

    def build_stage1_prompt(self, text: str) -> str:
        """
        公共方法：构建Stage 1的prompt
        供外部调用，避免直接访问私有方法
        """
        return self._build_stage1_prompt(text)

    async def simple_retrieval(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        公共方法：简化的检索方法
        供外部调用，避免直接访问私有方法
        """
        return await self._simple_retrieval(description, k)

    async def execute_ner_stage(self, text: str, few_shot_examples: List[Any]) -> Dict[str, List[str]]:
        """
        公共方法：执行NER阶段
        供外部调用，避免直接访问私有方法
        """
        return await self._execute_ner_stage(text, few_shot_examples)

    async def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """
        🚀 两阶段NER流程

        Stage 1: LLM接受input并做function call，用description和k调用检索器函数得到few-shot
        Stage 2: 用这些few-shot拼接成为prompt做NER
        """
        try:
            await self._ensure_initialized()

            logger.info(f"🧠 Stage 1: 分析文本并生成检索请求: '{text[:50]}...'")

            # Stage 1: LLM分析文本并生成检索请求
            stage1_prompt = self._build_stage1_prompt(text)
            tools: List[Type[BaseModel]] = [RetrieveNERExamplesTool]  # 只提供检索工具
            messages = [{"role": "user", "content": stage1_prompt}]

            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )

            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                # 执行检索获取few-shot
                few_shot_examples = await self._execute_retrieval_stage(response.tool_calls)

                if few_shot_examples:
                    logger.info(f"🧠 Stage 2: 基于{len(few_shot_examples)}个示例进行NER")
                    # Stage 2: 基于few-shot进行NER
                    return await self._execute_ner_stage(text, few_shot_examples)
                else:
                    logger.warning("❌ 未获取到few-shot示例")
                    return {}
            else:
                logger.warning("❌ LLM未调用检索工具")
                return {}

        except Exception as e:
            logger.error(f"两阶段NER失败: {e}")
            return {}

    async def _execute_retrieval_stage(self, tool_calls: List[Any]) -> List[Any]:
        """
        {{ AURA-X: Optimize - 添加跳过机制和多维度支持. Approval: 寸止(ID:1738230400). }}
        执行Stage 1的检索阶段 - 支持多维度参数但当前仍使用文本检索
        """
        for tool_call in tool_calls:
            if not tool_call.function or tool_call.function.name != "RetrieveNERExamplesTool":
                continue

            try:
                # 统一的JSON解析逻辑
                arguments = parse_tool_arguments(tool_call.function.arguments)
                if arguments is None:
                    logger.warning("参数解析失败，跳过此样本")
                    return []  # 直接跳过

                description = arguments.get("description", "")
                k = arguments.get("k", 3)

                # 提取多维度参数（当前仅记录，未来用于检索）
                dimensions = self._extract_dimensions(arguments)
                if dimensions:
                    logger.info(f"🧠 多维度分析: {dimensions}")

                # 执行检索（使用新的多维度接口，当前仍基于文本相似度）
                examples = await self.example_retriever.retrieve_with_dimensions(
                    query_text=description,
                    query_dimensions=dimensions,
                    k=k
                )
                logger.info(f"🔍 检索完成: {description[:30]}..., 返回{len(examples)}个示例")
                return examples

            except Exception as e:
                logger.error(f"检索阶段失败: {e}")
                return []  # 跳过失败的样本

        return []

    def _extract_dimensions(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取多维度特征参数
        当前仅用于日志记录，未来用于多维度检索
        """
        dimensions = {}

        if arguments.get("entity_density") is not None:
            dimensions["entity_density"] = arguments["entity_density"]

        if arguments.get("boundary_ambiguity") is not None:
            dimensions["boundary_ambiguity"] = arguments["boundary_ambiguity"]

        if arguments.get("dependency_depth") is not None:
            dimensions["dependency_depth"] = arguments["dependency_depth"]

        if arguments.get("additional_features"):
            dimensions.update(arguments["additional_features"])

        return dimensions

    async def _execute_ner_stage(self, text: str, few_shot_examples: List[Any]) -> Dict[str, List[str]]:
        """
        {{ AURA-X: Optimize - 强化prompt和解析. Approval: 寸止(ID:1738230400). }}
        执行Stage 2的NER阶段 - 强化prompt和多重解析
        """
        examples_text = self._format_examples_for_context(few_shot_examples)
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        ner_prompt = f"""You are an expert Named Entity Recognition system.

Extract named entities from text using ONLY the entity types specified below.

OUTPUT FORMAT REQUIREMENTS:
1. Return ONLY a valid JSON object
2. Keys must be entity types from the label set
3. Values must be arrays of entity strings
4. If no entities found for a type, use empty array []
5. If no entities found at all, return {{}}
6. NO explanations, NO additional text, ONLY JSON

Label set: {entity_types_str}

Examples (learn from these patterns):
{examples_text}

Text to analyze: "{text}"

JSON output:"""

        messages = [{"role": "user", "content": ner_prompt}]

        response = await self.model_service.generate_simple_async(
            messages=messages,
            temperature=0.0  # 使用0温度确保一致性
        )

        if response:
            entity_types = self._get_current_entity_types()
            return robust_json_parse_ner(response, entity_types)
        else:
            logger.warning("❌ Stage 2 NER失败")
            return {}

    async def _simple_retrieval(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        🔍 简化的检索方法 - 直接基于description检索k个示例
        """
        try:
            if not self.example_retriever or not self.example_retriever.initialized:
                logger.warning("⚠️ 示例检索器未初始化")
                return []

            # 直接使用description和k进行检索
            examples = await self.example_retriever.simple_retrieve(description, k)
            return examples

        except Exception as e:
            logger.error(f"简化检索失败: {e}")
            return []

    def _format_examples_for_context(self, examples) -> str:
        """
        {{ AURA-X: Optimize - 内联数据提取逻辑，减少函数调用. Approval: 寸止(ID:1738230400). }}
        优化的示例格式化方法
        """
        if not examples:
            return "No examples available."

        formatted_examples = []
        for i, example in enumerate(examples, 1):
            # 提取示例数据
            if hasattr(example, 'example'):
                example_data = example.example
            elif isinstance(example, dict):
                example_data = example
            else:
                example_data = {}
            if not example_data:
                continue

            text = example_data.get('text', '')
            labels = example_data.get('label', {})

            # 简化的实体格式化
            entities_str = self._format_entities(labels)
            formatted_examples.append(f"Example {i}:\nText: {text}\nEntities: [{entities_str}]")

        return "\n\n".join(formatted_examples)



    def _format_entities(self, labels: Dict[str, List[str]]) -> str:
        """
        简化的实体格式化
        """
        entities = []
        for etype, entities_list in labels.items():
            for entity in entities_list:
                entities.append(f"'{entity}' ({etype})")
        return ", ".join(entities)




# 🚀 全局实例管理
_meta_cognitive_agent = None

def get_meta_cognitive_agent(example_retriever=None):
    """获取元认知智能体实例"""
    global _meta_cognitive_agent
    if _meta_cognitive_agent is None:
        _meta_cognitive_agent = MetaCognitiveAgent(example_retriever)
    return _meta_cognitive_agent
</file>

<file path="main.py">
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 APIICL - 元认知智能体NER系统 (三阶段统一处理版本)
阶段1: 统一生成检索请求
阶段2: 统一检索
阶段3: 统一NER
"""

# {{ AURA-X: Fix - 解决OpenMP库冲突警告. Approval: 寸止(ID:1738230403). }}
import os
# 设置环境变量解决OpenMP重复库警告
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
# 可选：限制OpenMP线程数以提高稳定性
os.environ['OMP_NUM_THREADS'] = '1'

import asyncio
import argparse
import json
import gc
import traceback
from typing import Dict, Any, Optional, List, Type
from datetime import datetime

from config import CONFIG, set_dataset, list_available_datasets, get_current_dataset_info, initialize_datasets, get_cache_path
from schemas import RetrieveNERExamplesTool
from pydantic import BaseModel
from utils import async_read_json, async_write_json, safe_cancel_tasks, safe_cleanup_tasks, with_timeout, ProgressManager, setup_logging, print_banner, parse_tool_arguments



# 常量定义
DEFAULT_RETRIEVAL_DESCRIPTION = "general NER examples"


async def process_and_eval_dataset(max_samples: Optional[int] = None) -> Dict[str, Any]:
    """🎯 三阶段统一处理数据集"""

    # 创建进度管理器
    progress = ProgressManager()

    # 获取当前数据集信息
    current_dataset = get_current_dataset_info()
    dataset_path = current_dataset['path']

    # 检查测试集文件
    test_path = dataset_path.replace('train.json', 'test.json')
    if not os.path.exists(test_path):
        print(f"❌ 测试集文件不存在: {test_path}")
        return {}

    # 阶段0：数据准备和向量库预初始化
    progress.start_stage("📚 阶段0：数据准备和向量库预初始化", 3)

    try:
        test_data = await async_read_json(test_path)
        progress.update_progress(completed=1)
    except Exception as e:
        print(f"❌ 加载测试集失败: {e}")
        return {}

    # 限制样本数量
    if max_samples and max_samples < len(test_data):
        test_data = test_data[:max_samples]
    progress.update_progress(completed=2)

    # 预初始化向量库
    progress.log_message("🔍 预初始化向量库...")
    from example_retriever import ExampleRetriever
    global_retriever = ExampleRetriever()
    vector_ready = await global_retriever.initialize_vector_store()
    if vector_ready:
        progress.log_message("✅ 向量库预初始化成功")
    else:
        progress.log_message("⚠️ 向量库预初始化失败，将使用直接模式")

    progress.update_progress(completed=3)
    progress.finish_stage("数据准备阶段完成")
    
    # 初始化元认知智能体
    from meta_cognitive_agent import get_meta_cognitive_agent
    agent = get_meta_cognitive_agent(global_retriever)
    
    # 阶段1：为每个样本生成检索请求
    progress.start_stage("🧠 阶段1：生成检索请求", len(test_data))

    # 检查缓存
    cache_file = get_cache_path(f"requests_{len(test_data)}")

    if os.path.exists(cache_file):
        progress.log_message("📦 发现检索请求缓存，正在加载...")
        try:
            all_retrieval_requests = await async_read_json(cache_file)
            progress.log_message(f"✅ 从缓存加载了 {len(all_retrieval_requests)} 个检索请求")
            progress.update_progress(completed=len(all_retrieval_requests))
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ 缓存文件损坏，重新生成... 错误: {e}")
            all_retrieval_requests = None
    else:
        all_retrieval_requests = None

    if all_retrieval_requests is None:
        async def generate_single_request(i, sample):
            """{{ AURA-X: Optimize - 添加跳过机制. Approval: 寸止(ID:1738230400). }}
            并发生成单个检索请求 - 添加跳过机制"""
            text = sample.get('text', '')

            try:
                stage1_prompt = agent.build_stage1_prompt(text)
                tools: List[Type[BaseModel]] = [RetrieveNERExamplesTool]
                messages = [{"role": "user", "content": stage1_prompt}]

                response = await agent.model_service.generate_with_tools_async(
                    messages=messages,
                    tools=tools
                )

                if response and hasattr(response, 'tool_calls') and response.tool_calls:
                    for tool_call in response.tool_calls:
                        if tool_call.function.name == "RetrieveNERExamplesTool":
                            arguments = parse_tool_arguments(tool_call.function.arguments)
                            if arguments is None:
                                # 标记为跳过
                                return (i, "SKIP", 0)

                            description = arguments.get("description", DEFAULT_RETRIEVAL_DESCRIPTION)
                            k = arguments.get("k", 3)

                            # 提取并记录多维度参数
                            dimensions = {}
                            if arguments.get("entity_density") is not None:
                                dimensions["entity_density"] = arguments["entity_density"]
                            if arguments.get("boundary_ambiguity") is not None:
                                dimensions["boundary_ambiguity"] = arguments["boundary_ambiguity"]
                            if arguments.get("dependency_depth") is not None:
                                dimensions["dependency_depth"] = arguments["dependency_depth"]
                            if arguments.get("additional_features"):
                                dimensions.update(arguments["additional_features"])

                            if dimensions:
                                print(f"🧠 样本{i}多维度分析: {dimensions}")

                            return (i, description, k, dimensions)

                return (i, DEFAULT_RETRIEVAL_DESCRIPTION, 3, {})

            except Exception as e:
                print(f"⚠️ 样本 {i} 生成检索请求失败: {e}")
                return (i, "SKIP", 0, {})  # 标记失败样本为跳过

        batch_size = CONFIG.get('batch_size', 200)
        batch_delay = CONFIG.get('batch_delay', 1.0)
        all_batch_tasks = []

        for i in range(0, len(test_data), batch_size):
            batch_samples = test_data[i:i+batch_size]
            batch_indices = list(range(i, min(i+batch_size, len(test_data))))



            # 创建批次任务（不等待完成）
            batch_tasks = [
                generate_single_request(idx, sample)
                for idx, sample in zip(batch_indices, batch_samples)
            ]

            # 添加到总任务列表
            all_batch_tasks.extend(batch_tasks)

            # 批次发送间隔（不等待当前批次完成）
            if i + batch_size < len(test_data):
                await asyncio.sleep(batch_delay)



        # 使用as_completed显示实时进度
        all_results = []
        completed_count = 0

        for completed_task in asyncio.as_completed(all_batch_tasks):
            result = await completed_task
            all_results.append(result)
            completed_count += 1
            progress.update_progress(completed=completed_count)

        # 处理所有结果
        all_retrieval_requests = []
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ 任务失败: {result}")
                failed_count += 1
            else:
                all_retrieval_requests.append(result)
                success_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段1完成 - 成功: {len(all_retrieval_requests)}/{len(test_data)}")

        # 按样本ID排序，保持顺序
        all_retrieval_requests.sort(key=lambda x: x[0])

        # 过滤掉失败的请求（SKIP标记）
        valid_requests = [req for req in all_retrieval_requests if req[1] != "SKIP"]
        skipped_count = len(all_retrieval_requests) - len(valid_requests)
        if skipped_count > 0:
            print(f"⚠️ 跳过了 {skipped_count} 个失败的样本")
        all_retrieval_requests = valid_requests

        # 清理不再需要的大型列表，释放内存
        del all_batch_tasks, all_results
        # {{ AURA-X: Fix - 修复gc模块作用域问题. Approval: 寸止(ID:1754055919). }}
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存到缓存
        try:
            await async_write_json(cache_file, all_retrieval_requests)
            print(f"💾 检索请求已缓存到: {cache_file}")
        except Exception as e:
            print(f"⚠️ 缓存保存失败: {e}")
    
    print()
    
    # 阶段2：为每个样本执行检索
    progress.start_stage("🔍 阶段2：执行检索", len(all_retrieval_requests))

    # 检查检索结果缓存
    examples_cache_file = get_cache_path(f"examples_{len(test_data)}")

    if os.path.exists(examples_cache_file):
        progress.log_message("📦 发现检索结果缓存，正在加载...")
        try:
            all_examples = await async_read_json(examples_cache_file)
            # 转换字符串键为整数键
            all_examples = {int(k): v for k, v in all_examples.items()}
            progress.log_message(f"✅ 从缓存加载了 {len(all_examples)} 个检索结果")
            progress.update_progress(completed=len(all_examples))
            progress.finish_stage(f"阶段2完成 - 从缓存加载: {len(all_examples)}")
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ 检索缓存文件损坏，重新检索... 错误: {e}")
            all_examples = None
    else:
        all_examples = None

    if all_examples is None:
        batch_size = CONFIG.get('batch_size', 200)
        batch_delay = CONFIG.get('batch_delay', 1.0)

        @with_timeout(CONFIG.get('timeouts', {}).get('single_task', 180))  # 单任务超时
        async def retrieve_single_example(sample_id, description, k, dimensions):
            """并发检索单个示例 - 添加超时保护和多维度支持"""
            try:
                examples = await agent.example_retriever.retrieve_with_dimensions(
                    query_text=description,
                    query_dimensions=dimensions,
                    k=k
                )
                return (sample_id, examples)
            except Exception as e:
                progress.log_message(f"⚠️ 样本 {sample_id} 检索失败: {e}")
                return (sample_id, [])

        all_examples = {}

        all_retrieval_tasks = []

        for i in range(0, len(all_retrieval_requests), batch_size):
            batch_requests = all_retrieval_requests[i:i+batch_size]

            # 创建批次任务（不等待完成）
            batch_tasks = [
                retrieve_single_example(sample_id, description, k, dimensions)
                for sample_id, description, k, dimensions in batch_requests
            ]

            # 添加到总任务列表
            all_retrieval_tasks.extend(batch_tasks)

            # 批次发送间隔（不等待当前批次完成）
            if i + batch_size < len(all_retrieval_requests):
                await asyncio.sleep(batch_delay)



        # 创建任务对象以便跟踪进度
        tasks = [asyncio.create_task(coro) for coro in all_retrieval_tasks]
        all_results = []
        completed_count = 0

        try:
            # 使用as_completed显示实时进度，统一超时配置
            stage2_timeout = CONFIG.get('timeouts', {}).get('stage2_batch', 900)
            for completed_task in asyncio.as_completed(tasks, timeout=stage2_timeout):
                try:
                    result = await completed_task
                    all_results.append(result)
                except Exception as e:
                    all_results.append(e)

                completed_count += 1
                progress.update_progress(completed=completed_count)

        except asyncio.TimeoutError:
            progress.log_message("⚠️ 阶段2任务超时，使用部分结果继续")
            # 安全取消未完成的任务
            await safe_cancel_tasks(tasks)
            # 使用已完成的结果
            if len(all_results) < len(tasks):
                all_results.extend([Exception("Timeout")] * (len(tasks) - len(all_results)))

        finally:
            # 确保所有任务都被清理
            await safe_cleanup_tasks(tasks)

        # 处理所有结果
        all_examples = {}
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ 检索任务失败: {result}")
                failed_count += 1
            else:
                sample_id, examples = result
                all_examples[sample_id] = examples
                success_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段2完成 - 成功: {len(all_examples)}/{len(all_retrieval_requests)}")

        # 清理不再需要的大型列表，释放内存
        del all_retrieval_tasks, tasks, all_results
        # {{ AURA-X: Fix - 修复gc模块作用域问题. Approval: 寸止(ID:1754055919). }}
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存检索结果到缓存
        try:
            await async_write_json(examples_cache_file, all_examples)
            progress.log_message(f"💾 检索结果已缓存到: {examples_cache_file}")
        except Exception as e:
            progress.log_message(f"⚠️ 检索缓存保存失败: {e}")
    
    print()
    
    # 阶段3：为每个样本执行NER
    progress.start_stage("🎯 阶段3：执行NER", len(test_data))

    # 检查NER结果缓存
    ner_cache_file = get_cache_path(f"ner_results_{len(test_data)}")

    if os.path.exists(ner_cache_file):
        progress.log_message("📦 发现NER结果缓存，正在加载...")
        try:
            cached_results = await async_read_json(ner_cache_file)
            progress.log_message(f"✅ 从缓存加载了 {len(cached_results)} 个NER结果")
            results = cached_results
            progress.update_progress(completed=len(cached_results))
            progress.finish_stage(f"阶段3完成 - 从缓存加载: {len(cached_results)}")
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ NER缓存文件损坏，重新执行NER... 错误: {e}")
            results = None
    else:
        results = None

    if results is None:
        batch_size = CONFIG.get('batch_size', 200)
        batch_delay = CONFIG.get('batch_delay', 1.0)

        async def execute_single_ner(i, sample):
            """并发执行单个NER"""
            text = sample.get('text', '')
            true_labels = sample.get('label', {})
            examples = all_examples.get(i, [])

            try:
                # 执行NER
                predicted_labels = await agent.execute_ner_stage(text, examples)
            except Exception as e:
                print(f"⚠️ 样本 {i} NER失败: {e}")
                predicted_labels = {}

            if not isinstance(predicted_labels, dict):
                predicted_labels = {}

            # 计算指标
            sample_correct = 0
            sample_total = sum(len(entities) for entities in true_labels.values())
            sample_predicted = sum(len(entities) for entities in predicted_labels.values())

            for entity_type, true_entities in true_labels.items():
                predicted_entities_of_type = predicted_labels.get(entity_type, [])
                for entity in true_entities:
                    if entity in predicted_entities_of_type:
                        sample_correct += 1

            return {
                'text': text,
                'true_labels': true_labels,
                'predicted_labels': predicted_labels,
                'correct': sample_correct,
                'total_true': sample_total,
                'total_predicted': sample_predicted
            }

        results = []

        all_ner_tasks = []

        for i in range(0, len(test_data), batch_size):
            batch_samples = test_data[i:i+batch_size]
            batch_indices = list(range(i, min(i+batch_size, len(test_data))))



            # 创建批次任务（不等待完成）
            batch_tasks = [
                execute_single_ner(idx, sample)
                for idx, sample in zip(batch_indices, batch_samples)
            ]

            # 添加到总任务列表
            all_ner_tasks.extend(batch_tasks)

            # 批次发送间隔（不等待当前批次完成）
            if i + batch_size < len(test_data):
                await asyncio.sleep(batch_delay)



        # 创建任务对象以便跟踪进度
        tasks = [asyncio.create_task(coro) for coro in all_ner_tasks]
        all_results = []
        completed_count = 0

        try:
            # 使用as_completed显示实时进度，增加超时时间
            stage3_timeout = CONFIG.get('timeouts', {}).get('stage3_batch', 1800)
            for completed_task in asyncio.as_completed(tasks, timeout=stage3_timeout):
                try:
                    result = await completed_task
                    all_results.append(result)
                except Exception as e:
                    all_results.append(e)

                completed_count += 1
                progress.update_progress(completed=completed_count)

        except asyncio.TimeoutError:
            progress.log_message("⚠️ 阶段3任务超时，使用部分结果继续")
            # 安全取消未完成的任务
            await safe_cancel_tasks(tasks)
            # 使用已完成的结果
            if len(all_results) < len(tasks):
                all_results.extend([Exception("Timeout")] * (len(tasks) - len(all_results)))

        finally:
            # 确保所有任务都被清理
            await safe_cleanup_tasks(tasks)

        # 处理所有结果
        results = []
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ NER任务失败: {result}")
                failed_count += 1
            else:
                results.append(result)
                success_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段3完成 - 成功: {len(results)}/{len(test_data)}")

        # 清理不再需要的大型列表，释放内存
        del all_ner_tasks, tasks, all_results
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存NER结果到缓存
        try:
            await async_write_json(ner_cache_file, results)
            progress.log_message(f"💾 NER结果已缓存到: {ner_cache_file}")
        except Exception as e:
            progress.log_message(f"⚠️ NER缓存保存失败: {e}")

    # 计算汇总指标
    correct_predictions = sum(r['correct'] for r in results)
    total_entities = sum(r['total_true'] for r in results)
    predicted_entities = sum(r['total_predicted'] for r in results)
    
    print()

    # 计算最终指标
    precision = correct_predictions / predicted_entities if predicted_entities > 0 else 0
    recall = correct_predictions / total_entities if total_entities > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    # 显示最终评估结果
    print("\n" + "="*60)
    print("🎯 最终评估结果")
    print("="*60)
    print(f"📊 数据集: {current_dataset['name']}")
    print(f"📝 处理样本数: {len(test_data)}")
    print(f"🎯 真实实体总数: {total_entities}")
    print(f"🔍 预测实体总数: {predicted_entities}")
    print(f"✅ 正确预测数: {correct_predictions}")
    print("-" * 40)
    print(f"📈 Precision: {precision:.4f} ({correct_predictions}/{predicted_entities})")
    print(f"📈 Recall: {recall:.4f} ({correct_predictions}/{total_entities})")
    print(f"📈 F1-Score: {f1_score:.4f}")
    print("="*60)
    
    # 保存评估结果
    eval_results = {
        'dataset': current_dataset['name'],
        'timestamp': datetime.now().isoformat(),
        'samples_count': len(test_data),
        'total_entities': total_entities,
        'predicted_entities': predicted_entities,
        'correct_predictions': correct_predictions,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'processing_mode': 'unified_three_stage',
        'detailed_results': results
    }
    
    # 保存到文件
    results_dir = CONFIG.get('results_dir', './results')
    os.makedirs(results_dir, exist_ok=True)
    eval_file = os.path.join(results_dir, f"eval_results_unified_{current_dataset['name'].lower().replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    try:
        await async_write_json(eval_file, eval_results)
        print(f"💾 详细结果已保存到: {eval_file}")
    except Exception as e:
        print(f"⚠️ 保存结果失败: {e}")
    
    return eval_results


async def main():
    """主函数 - 三阶段统一处理"""
    parser = argparse.ArgumentParser(description='🧠 APIICL - 元认知智能体NER系统 (三阶段统一处理)')
    parser.add_argument('--dataset', '-d', type=str, default=CONFIG.get('dataset', 'ace2005'),
                       help=f'数据集名称 (默认: {CONFIG.get("dataset", "ace2005")})')
    parser.add_argument('--max-samples', type=int, default=CONFIG.get('max_test_samples'),
                       help=f'最大测试样本数 (默认: {CONFIG.get("max_test_samples", "处理全部")})')
    parser.add_argument('--log-level', type=str, default=CONFIG.get('log_level', 'WARNING'),
                       help=f'日志级别 (默认: {CONFIG.get("log_level", "WARNING")}) (DEBUG/INFO/WARNING/ERROR)')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)

    # 初始化数据集配置
    initialize_datasets()

    # 打印横幅
    print_banner()
    
    # 设置数据集
    if not set_dataset(args.dataset):
        print(f"❌ 数据集不存在: {args.dataset}")
        available = list_available_datasets()
        print("\n可用数据集:")
        for key, info in available.items():
            status = "✅" if info['available'] else "❌"
            current = "👈 当前" if info['current'] else ""
            print(f"  {status} {key}: {info['name']} {current}")
        return
    
    # 显示当前配置
    current_dataset = get_current_dataset_info()
    print(f"📊 数据集: {current_dataset['name']}")
    if args.max_samples:
        print(f"📝 最大样本数: {args.max_samples}")
    print()
    
    try:
        # 执行三阶段统一处理
        await process_and_eval_dataset(args.max_samples)
        
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
</file>

<file path="config.py">
import os

CONFIG = {
    # ====== 🚀 核心配置 ======
    'max_test_samples': 1500,
    'embedding_model_path': 'BAAI/bge-m3',

    # ====== 🔗 API配置 ======
    'base_url': 'http://8.138.94.162:3001/proxy/silicon/v1',
    'model_name': 'Qwen/Qwen2.5-7B-Instruct',
    'api_key': 'sk-zhongyushi',
    'max_concurrent_requests': 500,

    # ====== ⚡ 批处理配置 ======
    'batch_size': 64,  # 统一批次大小，用于API调用和嵌入
    'batch_delay': 1.0,  # 批次间延迟(秒)

    # ====== ⏱️ 超时配置 ======
    'timeouts': {
        'api_request': 120,      # 单个API请求超时
        'stage2_batch': 900,     # 阶段2批处理超时(15分钟)
        'stage3_batch': 1800,    # 阶段3批处理超时(30分钟)
        'single_task': 300,      # 单个任务超时(5分钟)
    },

    # ====== 🔄 重试配置 ======
    'retry': {
        'max_attempts': 3,       # 最大重试次数
        'delay_seconds': 5,      # 重试延迟(秒)
    },

    # ====== 📊 界面配置 ======
    'log_level': 'info',

    # ====== 📁 路径配置 ======
    'cache_dir': './cache',
    'data_root_dir': './data',

    # ====== 🔍 检索配置 ======
    'retrieval_config': {
        'vector_top_k': 30,
        'reranker_top_k': 10,
        'final_examples_count': 3,
        'score_threshold': 0.1,
        'diversity_lambda': 0.3,
    },

    # ====== 🧠 多维度检索配置 ======
    'multi_dimensional_config': {
        'enabled': True,  # 启用多维度检索功能
        # {{ AURA-X: Extend - 更新权重配置支持RRF融合. Approval: 寸止(ID:**********). }}
        'dimension_weights': {
            'text_similarity': 0.6,    # 文本相似度权重
            'rrf_fusion': 0.4,         # RRF融合权重
            # 传统维度权重（用于向后兼容）
            'entity_density': 0.1,
            'boundary_ambiguity': 0.1,
            'dependency_depth': 0.1,
            'formality_level': 0.05,
            'information_density': 0.05
        },
        'rrf_config': {
            'k_constant': 60,          # RRF常数
            'enable_parallel_search': True,  # 启用并行搜索
            'max_candidates_per_dim': 20     # 每个维度最大候选数
        },
        'precompute_dimensions': True,
        'dimension_cache_path': './cache/dimensions',
        'supported_dimensions': [
            'entity_density',
            'boundary_ambiguity',
            'dependency_depth',
            'rhetorical_role',
            'formality_level',
            'information_density',
            'instruction_complexity',
            'additional_features'
        ]
    },

    # ====== 📊 数据集配置 ======
    'dataset': 'conll2003',
    'current_dataset': 'conll2003',  # {{ AURA-X: Fix - 添加缺失的current_dataset配置. Approval: 寸止(ID:1738230405). }}
    'datasets': {
        'ace2005': {
            'name': 'ACE 2005',
            'path': 'data/ACE 2005/train.json',
            'labels': ['person', 'organization', 'location', 'facility', 'weapon', 'vehicle', 'geo-political'],
            'label_prompt': """**IMPORTANT: Use ONLY these entity types from ACE 2005 dataset:**
- person: People, individuals, groups (e.g., "John Smith", "Mary Johnson", "the team")
- organization: Companies, institutions, agencies (e.g., "Apple Inc.", "Microsoft", "FBI")
- location: Places, addresses, geographic locations (e.g., "New York", "California", "Main Street")
- facility: Buildings, structures, installations (e.g., "White House", "airport", "hospital")
- weapon: Weapons, armaments (e.g., "rifle", "missile", "bomb")
- vehicle: Transportation vehicles (e.g., "car", "airplane", "ship")
- geo-political: Geographic and political entities (e.g., "United States", "European Union", "NATO")"""
        },
        'conll2003': {
            'name': 'CoNLL 2003',
            'path': 'data/CoNLL2003/train.json',
            'labels': ['PER', 'ORG', 'LOC', 'MISC'],
            'label_prompt': """**IMPORTANT: Use ONLY these entity types from CoNLL 2003 dataset:**
- PER: Person names (e.g., "John Smith", "Mary Johnson")
- ORG: Organizations (e.g., "Apple Inc.", "Microsoft")
- LOC: Locations (e.g., "New York", "California")
- MISC: Miscellaneous named entities"""
        }
    }
}

# ====== 🛠️ 工具函数 ======
def set_dataset(dataset_key: str) -> bool:
    """切换数据集"""
    if dataset_key in CONFIG['datasets']:
        CONFIG['current_dataset'] = dataset_key
        return True
    return False

def get_current_dataset_info() -> dict:
    """获取当前数据集信息"""
    return CONFIG['datasets'][CONFIG['current_dataset']]

def get_current_dataset_path() -> str:
    """获取当前数据集路径"""
    return get_current_dataset_info()['path']

def get_current_label_prompt() -> str:
    """获取当前数据集的标签prompt"""
    return get_current_dataset_info()['label_prompt']

def list_available_datasets() -> dict:
    """列出所有可用数据集"""
    available = {}
    for key, info in CONFIG['datasets'].items():
        available[key] = {
            'name': info['name'],
            'available': os.path.exists(info['path']),
            'current': key == CONFIG['current_dataset'],
            'labels': info.get('labels', [])
        }
    return available





def get_dataset_cache_dir() -> str:
    """获取当前数据集的缓存目录"""
    dataset_name = get_current_dataset_info()['name'].lower().replace(' ', '_')
    cache_dir = CONFIG['cache_dir']
    dataset_cache_dir = f"{cache_dir}/{dataset_name}"
    os.makedirs(dataset_cache_dir, exist_ok=True)
    return dataset_cache_dir

def get_cache_path(cache_type: str) -> str:
    """获取缓存文件路径"""
    dataset_cache_dir = get_dataset_cache_dir()
    return f"{dataset_cache_dir}/{cache_type}.json"


def initialize_datasets():
    """初始化数据集配置"""
    print("🚀 初始化数据集配置...")
    print("✅ 数据集配置已加载")


# ====== 🧠 轻量级维度注册表 ======
# 把所有维度的配置都集中在这里，方便实验和调整

# 从你的计算器导入函数 (假设 dimension_calculator.py 在同一目录下)
try:
    from dimension_calculator import DimensionCalculator
    dim_calc = DimensionCalculator()

    DIMENSION_REGISTRY = {
        'entity_density': {
            'type': 'numerical',
            'calculator': dim_calc.calculate_entity_density,
            'similarity_params': {'sigma': 1.0, 'weight': 0.2}
        },
        'boundary_ambiguity': {
            'type': 'numerical',
            'calculator': dim_calc.calculate_boundary_ambiguity,
            'similarity_params': {'sigma': 0.3, 'weight': 0.2}
        },
        'dependency_depth': {
            'type': 'numerical',
            'calculator': lambda text, entities=None: float(dim_calc.calculate_dependency_depth(text)),
            'similarity_params': {'sigma': 2.0, 'weight': 0.2}
        },
        'formality_level': {
            'type': 'numerical',
            'calculator': dim_calc.calculate_formality_level,
            'similarity_params': {'sigma': 0.2, 'weight': 0.1}
        },
        'information_density': {
            'type': 'numerical',
            'calculator': dim_calc.calculate_information_density,
            'similarity_params': {'sigma': 0.3, 'weight': 0.1}
        },
        'rhetorical_role': {
            'type': 'categorical',
            'calculator': dim_calc.calculate_rhetorical_role,
            'similarity_params': {'weight': 0.1}
        },
        'instruction_complexity': {
            'type': 'categorical',
            'calculator': dim_calc.calculate_instruction_complexity,
            'similarity_params': {'weight': 0.1}
        }
    }
except ImportError as e:
    print(f"⚠️ 无法导入DimensionCalculator: {e}")
    DIMENSION_REGISTRY = {}
</file>

<file path="model_interface.py">
import logging
import asyncio
from typing import List, Dict, Any, Optional, Type
from pydantic import BaseModel

from openai import AsyncOpenAI
import aiohttp
from config import CONFIG

# 配置日志
logger = logging.getLogger(__name__)

def pydantic_to_openai_tool(pydantic_model: Type[BaseModel]) -> Dict[str, Any]:
    """将Pydantic模型转换为OpenAI Function Calling工具的JSON Schema。"""
    # Pydantic v2 has .model_json_schema()
    schema = pydantic_model.model_json_schema()
    return {
        "type": "function",
        "function": {
            "name": schema.get('title', pydantic_model.__name__),
            "description": schema.get('description', ''),
            "parameters": schema
        }
    }

class ModelService:
    """
    统一的模型服务层 - 极简版
    """
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(ModelService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.config = CONFIG
            self.base_url = self.config.get('base_url')
            self.model_name = self.config.get('model_name')
            self.api_key = self.config.get('api_key')
            self.timeout = self.config.get('timeouts', {}).get('api_request', 60)

            self.max_concurrent_requests = self.config.get('max_concurrent_requests', 500)
            self.semaphore = asyncio.Semaphore(self.max_concurrent_requests)

            retry_config = self.config.get('retry', {})
            self.api_max_retries = retry_config.get('max_attempts', 3)
            self.api_retry_delay = retry_config.get('delay_seconds', 2)

            # HTTP连接池优化，复用连接
            self._client_pool = {}  # 连接池，复用AsyncOpenAI客户端

            self.initialized = True
            logger.info(f"ModelService (优化版) initialized: concurrent_requests={self.max_concurrent_requests}, timeout={self.timeout}s")

    async def _get_client(self) -> AsyncOpenAI:
        # 实现连接池复用，提升性能
        client_key = f"{self.base_url}_{(self.api_key or '')[:10]}"
        if client_key not in self._client_pool:
            self._client_pool[client_key] = AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url,
                timeout=self.timeout,
            )
        return self._client_pool[client_key]

    async def generate_with_tools_async(self, messages: List[Dict], tools: List[Type[BaseModel]]):
        """
        异步调用LLM，并使用Function Calling。
        """
        if not self.api_key:
            logger.error("No API key configured.")
            return None

        tool_schemas = [pydantic_to_openai_tool(tool) for tool in tools]

        async with self.semaphore:
            for attempt in range(self.api_max_retries):
                try:
                    client = await self._get_client()
                    response = await client.chat.completions.create(
                        model=self.model_name,
                        messages=messages,
                        tools=tool_schemas,
                        tool_choice="auto",  # 让LLM自主选择调用工具
                    )
                    return response.choices[0].message

                except Exception as e:
                    logger.warning(f"API call failed on attempt {attempt + 1}: {e}")
                    if "context canceled" in str(e).lower():
                        logger.error(f"❌ Context canceled detected on attempt {attempt + 1}: {e}")
                    if attempt < self.api_max_retries - 1:
                        await asyncio.sleep(self.api_retry_delay)
                    else:
                        logger.error("API call failed after multiple retries.")
                        return None
    
    async def get_embeddings_async(self, texts: List[str]) -> List[List[float]]:
        """🔍 异步获取文本嵌入向量 - 支持大批量处理"""
        if not texts or not self.api_key:
            return []

        model_to_use = self.config['embedding_model_path']

        # 嵌入API批处理，避免超过64个限制
        max_batch_size = CONFIG.get('embedding_batch_size', 64)  # 从配置获取嵌入API批处理大小

        # 如果文本数量超过限制，分批处理
        if len(texts) > max_batch_size:
            logger.info(f"🔄 嵌入批处理: {len(texts)}个文本分为{(len(texts) + max_batch_size - 1) // max_batch_size}批")
            all_embeddings = []

            for i in range(0, len(texts), max_batch_size):
                batch_texts = texts[i:i + max_batch_size]
                batch_embeddings = await self._get_single_batch_embeddings(batch_texts, model_to_use)
                all_embeddings.extend(batch_embeddings)

                # 批次间短暂延迟，避免API限制
                if i + max_batch_size < len(texts):
                    await asyncio.sleep(0.1)

            return all_embeddings
        else:
            # 单批处理
            return await self._get_single_batch_embeddings(texts, model_to_use)

    async def _get_single_batch_embeddings(self, texts: List[str], model_to_use: str) -> List[List[float]]:
        """获取单批嵌入向量"""
        async with self.semaphore:
            for attempt in range(self.api_max_retries):
                try:
                    client = await self._get_client()
                    response = await client.embeddings.create(model=model_to_use, input=texts)
                    logger.debug(f"✅ Embeddings generated for {len(texts)} texts")
                    return [item.embedding for item in response.data]

                except Exception as e:
                    logger.warning(f"⚠️ Embedding call failed on attempt {attempt + 1}: {e}")
                    if attempt < self.api_max_retries - 1:
                        await asyncio.sleep(self.api_retry_delay)
                    else:
                        logger.error("❌ Embedding call failed after multiple retries.")
                        return []
        return []

    # 重排器功能已废弃，当前系统直接使用FAISS检索结果

    async def generate_simple_async(self, messages: List[Dict], temperature: float = 0.1) -> str:
        """🧠 简单文本生成 - 用于元认知规划器的三阶段Prompt链"""
        if not self.api_key:
            logger.error("❌ No API key configured for text generation")
            return ""

        async with self.semaphore:
            for attempt in range(self.api_max_retries):
                try:
                    client = await self._get_client()
                    response = await client.chat.completions.create(
                        model=self.model_name,
                        messages=messages,
                        temperature=temperature,
                        max_tokens=1000  # 适合规划阶段的输出长度
                    )

                    content = response.choices[0].message.content or ""
                    logger.debug(f"✅ Generated {len(content)} characters of text")
                    return content

                except Exception as e:
                    logger.warning(f"⚠️ Simple generation failed on attempt {attempt + 1}: {e}")
                    if attempt < self.api_max_retries - 1:
                        await asyncio.sleep(self.api_retry_delay)
                    else:
                        logger.error("❌ Simple generation failed after multiple retries.")
                        return ""
        return ""

    # ====== 🚀 真正的并发API调用 ======

    async def batch_generate_async(self, requests: List[Dict[str, Any]]) -> List[Any]:
        """
        🚀 批量并发API调用 - 真正的并发，不等待单个响应

        Args:
            requests: 请求列表，每个请求包含 {'type': 'chat'/'embedding', 'params': {...}}

        Returns:
            List[Any]: 对应的响应结果列表
        """
        if not requests:
            return []

        logger.info(f"🚀 Starting batch concurrent requests: {len(requests)} requests")

        # 创建所有任务，不等待
        tasks = []
        for i, request in enumerate(requests):
            request_type = request.get('type')
            params = request.get('params', {})

            if request_type == 'chat':
                task = self._single_chat_request(params)
            elif request_type == 'embedding':
                task = self._single_embedding_request(params)
            # 重排器功能已废弃
            else:
                logger.warning(f"⚠️ Unknown request type: {request_type}")
                task = asyncio.create_task(self._dummy_request())

            tasks.append(task)

        # 批量发送，等待所有结果
        logger.info(f"🚀 Sending {len(tasks)} concurrent requests...")
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Request {i} failed: {result}")
                processed_results.append(None)
            else:
                processed_results.append(result)

        success_count = sum(1 for r in processed_results if r is not None)
        logger.info(f"🎉 Batch requests completed: {success_count}/{len(requests)} successful")

        return processed_results

    async def _single_chat_request(self, params: Dict[str, Any]) -> Any:
        """单个聊天请求"""
        messages = params.get('messages', [])
        tools = params.get('tools', [])
        temperature = params.get('temperature', 0.1)

        if tools:
            return await self.generate_with_tools_async(messages, tools)
        else:
            return await self.generate_simple_async(messages, temperature)

    async def _single_embedding_request(self, params: Dict[str, Any]) -> List[List[float]]:
        """单个嵌入请求"""
        texts = params.get('texts', [])
        return await self.get_embeddings_async(texts)

    # 重排器功能已废弃

    async def _dummy_request(self) -> None:
        """虚拟请求，用于处理未知类型"""
        await asyncio.sleep(0.1)
        return None


# 全局单例
model_service = ModelService()
</file>

<file path="utils.py">
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🛠️ 工具函数模块
集中管理通用工具函数，遵循KISS原则
"""

import asyncio
import json
import re
import logging
import os
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

# ==================== JSON处理工具 ====================

def fix_common_json_errors(json_str: str) -> str:
    """修复常见的JSON格式错误"""
    # 移除多余的文本
    json_str = re.sub(r'^[^{]*', '', json_str)  # 移除开头非JSON部分
    json_str = re.sub(r'[^}]*$', '', json_str)  # 移除结尾非JSON部分
    
    # 修复单引号
    json_str = json_str.replace("'", '"')
    
    # 修复缺少引号的键
    json_str = re.sub(r'(\w+):', r'"\1":', json_str)
    
    return json_str


def clean_json_response(response: str) -> str:
    """清理JSON响应，移除markdown等格式"""
    # 移除markdown代码块
    response = re.sub(r'```json\s*', '', response)
    response = re.sub(r'```', '', response)
    
    # 提取JSON部分
    json_match = re.search(r'\{.*\}', response, re.DOTALL)
    if json_match:
        json_str = json_match.group()
        # 修复常见错误
        json_str = json_str.replace("'", '"')  # 单引号转双引号
        json_str = re.sub(r',\s*}', '}', json_str)  # 移除尾随逗号
        json_str = re.sub(r',\s*]', ']', json_str)  # 移除数组尾随逗号
        return json_str
    return "{}"


def parse_tool_arguments(arguments_str: str) -> Optional[Dict[str, Any]]:
    """
    统一的工具参数解析 - 增强容错版本
    返回None表示解析失败，应跳过此样本
    """
    try:
        # 尝试直接解析
        return json.loads(arguments_str)
    except json.JSONDecodeError:
        try:
            # 尝试修复常见JSON错误
            fixed_json = fix_common_json_errors(arguments_str)
            return json.loads(fixed_json)
        except Exception:
            logger.warning("工具参数JSON解析失败，标记为跳过")
            return None  # 返回None表示跳过


def robust_json_parse_ner(response: str, entity_types: List[str]) -> Dict[str, List[str]]:
    """
    增强的NER结果JSON解析 - 多重容错策略
    专门用于解析NER任务的JSON响应
    """
    try:
        # 尝试1: 直接解析
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if json_match:
            entities_json = json_match.group()
            entities = json.loads(entities_json)
            logger.info(f"✅ NER解析完成，提取到 {sum(len(v) for v in entities.values())} 个实体")
            return entities
    except json.JSONDecodeError:
        pass

    try:
        # 尝试2: 修复常见错误后解析
        cleaned_response = clean_json_response(response)
        entities = json.loads(cleaned_response)
        logger.info(f"✅ NER解析完成(修复后)，提取到 {sum(len(v) for v in entities.values())} 个实体")
        return entities
    except (json.JSONDecodeError, KeyError, TypeError):
        pass

    try:
        # 尝试3: 基于模式提取
        entities = extract_entities_by_pattern(response, entity_types)
        if entities:
            logger.info(f"✅ NER解析完成(模式提取)，提取到 {sum(len(v) for v in entities.values())} 个实体")
            return entities
    except (AttributeError, KeyError, TypeError):
        pass

    logger.error(f"NER结果解析失败，原始响应: {response[:200]}...")
    return {}


# ==================== 异步文件操作工具 ====================

# 检测aiofiles可用性
try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False


async def async_read_json(file_path: str) -> Any:
    """异步读取JSON文件"""
    if AIOFILES_AVAILABLE:
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
            return json.loads(content)
    else:
        # 回退到线程池中的同步操作
        def _sync_read():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)

        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            return await asyncio.get_event_loop().run_in_executor(executor, _sync_read)


async def async_write_json(file_path: str, data: Any) -> None:
    """异步写入JSON文件"""
    if AIOFILES_AVAILABLE:
        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            content = json.dumps(data, ensure_ascii=False, indent=2)
            await f.write(content)
    else:
        # 回退到线程池中的同步操作
        def _sync_write():
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            await asyncio.get_event_loop().run_in_executor(executor, _sync_write)


# ==================== 异步任务管理工具 ====================

async def safe_cancel_tasks(tasks: List[asyncio.Task]) -> None:
    """安全取消任务列表"""
    cancelled_tasks = []
    for task in tasks:
        if not task.done():
            task.cancel()
            cancelled_tasks.append(task)

    # 等待被取消的任务完成清理
    if cancelled_tasks:
        await asyncio.gather(*cancelled_tasks, return_exceptions=True)


async def safe_cleanup_tasks(tasks: List[asyncio.Task]) -> None:
    """确保任务完全清理"""
    if not tasks:
        return

    # 取消所有未完成的任务
    for task in tasks:
        if not task.done():
            task.cancel()

    # 等待所有任务完成（包括清理）
    await asyncio.gather(*tasks, return_exceptions=True)


def with_timeout(timeout_seconds: int):
    """为异步函数添加超时保护的装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
            except asyncio.TimeoutError:
                logger.warning(f"🚨 函数 {func.__name__} 执行超时({timeout_seconds}s)")
                raise
        return wrapper
    return decorator


# ==================== 进度管理工具 ====================

class ProgressManager:
    """🎯 进度管理器 - 美化和管理进度显示的逻辑"""

    def __init__(self):
        self._lock = threading.Lock()
        self._current_stage = None
        self._stage_name = ""
        self._total_tasks = 0
        self._completed = 0
        self._success = 0
        self._failed = 0
        self._start_time = None
        self._spinner_frame = 0
        self._animation_frame = 0

        # 旋转器字符
        self._spinner_chars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']

        # ANSI转义序列
        self._CLEAR_LINE = '\033[2K'
        self._CURSOR_UP = '\033[1A'
        self._CURSOR_DOWN = '\033[1B'
        self._SAVE_CURSOR = '\033[s'
        self._RESTORE_CURSOR = '\033[u'

    def start_stage(self, stage_name: str, total_tasks: int = 0):
        """开始一个新阶段"""
        with self._lock:
            self._stage_name = stage_name
            self._total_tasks = total_tasks
            self._completed = 0
            self._success = 0
            self._failed = 0
            self._start_time = datetime.now()
            self._current_stage = True

            # 显示阶段开始信息
            print(f"\n{stage_name}")
            if total_tasks > 0:
                self._render_progress()

    def update_progress(self, completed: Optional[int] = None, success: Optional[int] = None,
                       failed: Optional[int] = None, increment: bool = False):
        """更新进度"""
        with self._lock:
            if not self._current_stage:
                return

            if increment:
                # 增量模式
                if completed is not None:
                    self._completed += completed
                if success is not None:
                    self._success += success
                if failed is not None:
                    self._failed += failed
            else:
                # 绝对值模式
                if completed is not None:
                    self._completed = completed
                if success is not None:
                    self._success = success
                if failed is not None:
                    self._failed = failed

            self._render_progress()

    def finish_stage(self, final_message: Optional[str] = None):
        """结束当前阶段"""
        with self._lock:
            if not self._current_stage:
                return

            self._current_stage = False

            # 显示最终进度
            self._render_progress(final=True)

            # 显示完成信息
            if final_message:
                print(f"✅ {final_message}")

            # 计算耗时
            if self._start_time:
                duration = datetime.now() - self._start_time
                print(f"⏱️  耗时: {duration.total_seconds():.1f}秒")

            print()  # 额外换行

    def _render_progress(self, final: bool = False, width: int = 50):
        """渲染进度条"""
        if self._total_tasks <= 0:
            # 无总数的简单进度显示
            if final:
                spinner = "✅"
            else:
                self._spinner_frame = (self._spinner_frame + 1) % len(self._spinner_chars)
                spinner = self._spinner_chars[self._spinner_frame]

            status_parts = []
            if self._completed > 0:
                status_parts.append(f"已完成: {self._completed}")
            if self._success > 0:
                status_parts.append(f"成功: {self._success}")
            if self._failed > 0:
                status_parts.append(f"失败: {self._failed}")

            status = " | ".join(status_parts) if status_parts else "处理中..."
            print(f"\r{spinner} {status}", end='', flush=True)

            if final:
                print()  # 完成时换行
            return

        # 有总数的详细进度条
        percentage = self._completed / self._total_tasks if self._total_tasks > 0 else 0
        filled = int(width * percentage)

        # 动态效果：让已填充部分有流动效果
        if not final and self._completed < self._total_tasks and filled > 0:
            self._animation_frame = (self._animation_frame + 1) % 3
            if self._animation_frame == 0:
                bar = '█' * filled + '░' * (width - filled)
            elif self._animation_frame == 1:
                bar = '▓' * filled + '░' * (width - filled)
            else:
                bar = '▒' * filled + '░' * (width - filled)
        else:
            # 完成时显示实心
            bar = '█' * filled + '░' * (width - filled)

        # 旋转器
        if final:
            spinner = "✅"
        else:
            self._spinner_frame = (self._spinner_frame + 1) % len(self._spinner_chars)
            spinner = self._spinner_chars[self._spinner_frame]

        # 构建状态信息
        status_parts = [f"{self._completed}/{self._total_tasks}"]
        if self._success > 0:
            status_parts.append(f"成功: {self._success}")
        if self._failed > 0:
            status_parts.append(f"失败: {self._failed}")

        status = " | ".join(status_parts)

        # 显示进度条
        print(f"\r{spinner} [{bar}] {percentage:.1%} ({status})", end='', flush=True)

        if final:
            print()  # 完成时换行

    def log_message(self, message: str):
        """在不干扰进度条的情况下输出日志信息"""
        with self._lock:
            if self._current_stage:
                # 清除当前进度条，输出消息，然后在新行重新渲染进度条
                print(f"\r{self._CLEAR_LINE}", end="")  # 清除当前行
                print(message)  # 输出消息并自动换行
                self._render_progress()
            else:
                print(message)


# ==================== 日志设置工具 ====================

def setup_logging(level: str = "WARNING"):
    """设置日志配置"""
    import sys
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S',
        stream=sys.stderr
    )


# ==================== 字符串处理工具 ====================

def extract_entities_by_pattern(response: str, entity_types: List[str]) -> Dict[str, List[str]]:
    """基于模式提取实体（备用方案）"""
    entities = {etype: [] for etype in entity_types}
    
    # 简单模式匹配提取
    for etype in entity_types:
        pattern = rf'{etype}["\s]*:[\s]*\[(.*?)\]'
        matches = re.findall(pattern, response, re.IGNORECASE | re.DOTALL)
        if matches:
            for match in matches:
                items = re.findall(r'"([^"]+)"', match)
                entities[etype].extend(items)
    
    return entities


def print_banner():
    """打印系统横幅"""
    print("=" * 60)
    print("🧠 APIICL - 元认知智能体NER系统 (三阶段处理)")
    print("📚 阶段1: 生成检索请求 → 阶段2: 执行检索 → 阶段3: 执行NER")
    print("=" * 60)
</file>

</files>
