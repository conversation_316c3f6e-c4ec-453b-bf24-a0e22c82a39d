"""
示例检索器 - 简单高效的NER示例检索系统
遵循KISS原则：保持简单，避免过度设计
"""

import asyncio
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import json
import os
import time
import pickle
import hashlib


try:
    import faiss
    FAISS_AVAILABLE = True
    # 设置FAISS多线程数量为CPU核心数的一半，避免过度竞争
    cpu_count = os.cpu_count() or 4
    faiss_threads = max(1, cpu_count // 2)
    try:
        faiss.omp_set_num_threads(faiss_threads)
        logging.info(f"✅ FAISS多线程已启用: {faiss_threads} 线程")
    except AttributeError:
        # 某些FAISS版本可能没有omp_set_num_threads
        logging.info("✅ FAISS已加载，使用默认线程配置")
except ImportError:
    FAISS_AVAILABLE = False
    logging.warning("FAISS not available, falling back to simple vector search")


from model_interface import model_service
from config import CONFIG, get_current_dataset_path, DIMENSION_REGISTRY
from dimension_calculator import DimensionCalculator

logger = logging.getLogger(__name__)


class BatchProcessor:
    """{{ AURA-X: Fix - 重构为真正并发的批处理器. Approval: 寸止(ID:1754056919). }}
    高性能并发批处理器 - 支持真正并发"""

    def __init__(self, batch_size: int = 16, timeout: float = 0.1):
        self.batch_size = batch_size
        self.timeout = timeout  # 大幅减少等待时间
        self.request_futures = {}
        self.lock = asyncio.Lock()
        # {{ AURA-X: Fix - 移除串行处理标志，支持并发. Approval: 寸止(ID:1754056919). }}
        self.semaphore = asyncio.Semaphore(10)  # 限制并发数避免过载

    async def add_request(self, request_id: str, description: str) -> List[float]:
        """{{ AURA-X: Fix - 重构为并发处理. Approval: 寸止(ID:1754056919). }}
        并发处理嵌入请求 - 真正的并发版本"""

        # {{ AURA-X: Fix - 使用信号量控制并发，避免过载. Approval: 寸止(ID:1754056919). }}
        async with self.semaphore:
            try:
                # 直接调用API，不再使用复杂的批处理逻辑
                logger.debug(f"🔍 直接处理嵌入请求: {request_id}")
                embeddings = await asyncio.wait_for(
                    model_service.get_embeddings_async([description]),
                    timeout=60.0  # 单个请求60秒超时
                )

                if embeddings and len(embeddings) > 0:
                    logger.debug(f"✅ 嵌入请求完成: {request_id}")
                    return embeddings[0]  # 返回第一个（也是唯一的）嵌入
                else:
                    logger.warning(f"⚠️ 嵌入请求返回空结果: {request_id}")
                    return []

            except asyncio.TimeoutError:
                logger.warning(f"🚨 嵌入请求超时: {request_id}")
                raise
            except Exception as e:
                logger.error(f"🚨 嵌入请求失败: {request_id}, 错误: {e}")
                return []

    # {{ AURA-X: Fix - 移除批处理方法，改为直接并发处理. Approval: 寸止(ID:1754056919). }}
    # 批处理方法已移除，现在使用直接并发处理

    # {{ AURA-X: Fix - 移除批处理辅助方法，不再需要. Approval: 寸止(ID:1754056919). }}
    # 批处理辅助方法已移除，现在使用直接并发处理




class FAISSVectorStore:
    """FAISS高性能向量存储 - 并发安全版本"""

    def __init__(self):
        self.examples = []
        self.metadata = []
        self.embeddings = []
        self.index = None
        self.dimension = None
        self.initialized = False
        # {{ AURA-X: Fix - 避免锁初始化的竞态条件. Approval: 寸止(ID:1738230400). }}
        # 使用threading.Lock来保护asyncio.Lock的初始化
        import threading
        self._init_lock = threading.Lock()
        self._search_lock = None

    def add_examples(self, examples: List[Dict], embeddings: List[List[float]], metadata: List[Dict]):
        """添加示例和向量"""
        self.examples.extend(examples)
        self.metadata.extend(metadata)
        self.embeddings.extend(embeddings)

        if not self.dimension and embeddings:
            self.dimension = len(embeddings[0])

        self._build_index()

    def _build_index(self):
        """构建FAISS索引"""
        if not self.embeddings or not FAISS_AVAILABLE:
            return

        try:
            # 转换为numpy数组
            embeddings_array = np.array(self.embeddings, dtype=np.float32)

            # 创建FAISS索引
            if self.dimension:
                # {{ AURA-X: Fix - 强制使用简单索引避免IVF卡死. Approval: 寸止(ID:1754055919). }}
                # 暂时强制使用简单索引，避免IVF索引卡死问题
                logger.info(f"🔍 使用IndexFlatIP索引: {len(self.embeddings)}个向量")
                self.index = faiss.IndexFlatIP(self.dimension)
                # IndexFlatIP不需要训练和设置nprobe，它是精确搜索

                self.index.add(embeddings_array)
                self.initialized = True
                logger.info(f"✅ FAISS索引构建完成: {len(self.embeddings)}个向量, 维度{self.dimension}, 多线程已启用")

        except Exception as e:
            logger.error(f"FAISS索引构建失败: {e}")
            self.initialized = False

    async def search(self, query_embedding: List[float], top_k: int = 20) -> List[Tuple[int, float]]:
        """{{ AURA-X: Fix - 简化FAISS检索，移除复杂锁机制. Approval: 寸止(ID:1754055919). }}
        FAISS向量检索 - 简化版本"""
        if not self.initialized or not self.index:
            logger.warning("FAISS未初始化或索引不存在")
            return []

        try:
            logger.info(f"🔍 执行FAISS检索: top_k={top_k}, 索引大小={len(self.embeddings)}")

            # {{ AURA-X: Fix - 添加详细调试信息. Approval: 寸止(ID:1754055919). }}
            def _sync_search():
                logger.info(f"🔍 准备查询数组: 嵌入长度={len(query_embedding)}")
                query_array = np.array([query_embedding], dtype=np.float32)
                logger.info(f"🔍 查询数组形状: {query_array.shape}")
                logger.info(f"🔍 索引类型: {type(self.index)}")
                logger.info("🔍 开始调用index.search...")
                result = self.index.search(query_array, min(top_k, len(self.embeddings)))
                logger.info("🔍 index.search调用完成")
                return result

            # 在线程池中执行同步的FAISS检索
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                logger.info("🔍 提交到线程池执行...")
                scores, indices = await asyncio.get_event_loop().run_in_executor(executor, _sync_search)
                logger.info("🔍 线程池执行完成")

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx >= 0:  # FAISS返回-1表示无效结果
                    results.append((int(idx), float(score)))

            logger.info(f"🔍 FAISS检索成功: 返回{len(results)}个结果")
            return results

        except Exception as e:
            logger.error(f"FAISS检索失败: {e}")
            import traceback
            logger.error(f"FAISS检索失败堆栈: {traceback.format_exc()}")
            return []


class ExampleRetriever:
    """高性能NER任务示例检索器 - 支持FAISS+批处理"""

    def __init__(self):
        self.config = CONFIG.get('retrieval_config', {})
        self.model_service = model_service

        # 选择向量存储后端
        if not FAISS_AVAILABLE:
            raise ImportError("FAISS is not available, which is required for the vector store. Please install it using 'pip install faiss-cpu' or 'pip install faiss-gpu'.")
        self.vector_store = FAISSVectorStore()

        # 统一批处理参数，从CONFIG获取配置
        embedding_batch_size = min(CONFIG.get('batch_size', 32), 16)  # 减小批次大小
        batch_timeout = min(CONFIG.get('batch_delay', 1.0) * 0.3, 0.3)  # 减小超时时间
        self.batch_processor = BatchProcessor(batch_size=embedding_batch_size, timeout=batch_timeout)
        self.initialized = False

        # 初始化维度计算器
        self.dimension_calculator = DimensionCalculator()

        logger.info("示例检索器初始化完成")




    async def _load_pkl_cache(self, pkl_file: str, data_path: str) -> bool:
        """加载pkl缓存"""
        try:
            # 在线程池中执行pickle加载，避免阻塞事件循环
            def _load_pickle():
                with open(pkl_file, 'rb') as f:
                    return pickle.load(f)

            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                cached_data = await asyncio.get_event_loop().run_in_executor(executor, _load_pickle)

            # 验证缓存
            if not self._validate_cache(cached_data, data_path):
                logger.warning("缓存验证失败，重新生成...")
                return False

            # {{ AURA-X: Fix - 检查缓存版本和metadata完整性. Approval: 寸止(ID:1738230402). }}
            cache_version = cached_data.get('version', '1.0')
            if cache_version < '1.1':
                logger.warning(f"缓存版本过旧 ({cache_version})，缺少metadata，重新生成...")
                return False

            examples = cached_data['examples']
            embeddings = cached_data['embeddings']
            metadata = cached_data.get('metadata', None)

            # 严格检查metadata完整性
            if metadata is None or len(metadata) != len(examples):
                logger.warning(f"metadata不完整: 预期{len(examples)}个，实际{len(metadata) if metadata else 0}个，重新生成...")
                return False

            # 验证metadata结构
            for i, meta in enumerate(metadata):
                if not isinstance(meta, dict) or not meta:
                    logger.warning(f"metadata[{i}]为空或格式错误，重新生成...")
                    return False
                # 检查必要的维度字段
                required_dims = ['entity_density', 'boundary_ambiguity', 'dependency_depth']
                if not all(dim in meta for dim in required_dims):
                    logger.warning(f"metadata[{i}]缺少必要维度字段，重新生成...")
                    return False

            self.vector_store.add_examples(examples, embeddings, metadata)

            logger.info(f"从pkl缓存加载 {len(examples)} 个示例，包含完整metadata")
            self.initialized = True
            return True

        except Exception as e:
            logger.warning(f"加载pkl缓存失败: {e}")
            return False

    async def _migrate_json_to_pkl(self, json_file: str, pkl_file: str, data_path: str) -> bool:
        """将JSON缓存迁移到pkl格式"""
        try:
            # 在线程池中执行文件操作，避免阻塞事件循环
            def _load_json():
                with open(json_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                json_data = await asyncio.get_event_loop().run_in_executor(executor, _load_json)

            # {{ AURA-X: Fix - JSON迁移时重新计算metadata. Approval: 寸止(ID:1738230402). }}
            # 旧JSON缓存没有metadata，需要重新计算
            logger.info("🧠 JSON缓存缺少metadata，重新计算维度特征...")

            examples = json_data['examples']
            metadata = []
            for i, example in enumerate(examples):
                text = example.get('text', '')

                # 转换label格式为entities格式
                entities = []
                labels = example.get('label', {})
                for entity_type, entity_list in labels.items():
                    for entity_text in entity_list:
                        start_pos = text.find(entity_text)
                        if start_pos != -1:
                            entities.append({
                                'text': entity_text,
                                'label': entity_type,
                                'start': start_pos,
                                'end': start_pos + len(entity_text)
                            })

                # 计算维度特征
                dimensions = self.dimension_calculator.calculate_all_dimensions(text, entities)
                metadata.append(dimensions)

                if (i + 1) % 100 == 0:
                    logger.info(f"🧠 JSON迁移: 已计算 {i + 1}/{len(examples)} 个样本的维度特征")

            # 添加版本信息
            pkl_data = {
                'examples': examples,
                'embeddings': json_data['embeddings'],
                'metadata': metadata,  # 包含重新计算的metadata
                'dataset_path': data_path,
                'created_at': json_data.get('created_at', time.time()),
                'version': '1.1',  # 升级版本
                'dataset_hash': self._get_dataset_hash(data_path)
            }

            # 在线程池中保存pkl格式
            def _save_pickle():
                with open(pkl_file, 'wb') as f:
                    pickle.dump(pkl_data, f)
            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                await asyncio.get_event_loop().run_in_executor(executor, _save_pickle)

            # 删除旧的JSON缓存
            os.remove(json_file)

            # 加载到向量存储
            # {{ AURA-X: Fix - 使用重新计算的完整metadata. Approval: 寸止(ID:1738230402). }}
            self.vector_store.add_examples(pkl_data['examples'], pkl_data['embeddings'], pkl_data['metadata'])
            logger.info(f"成功迁移并加载 {len(pkl_data['examples'])} 个示例，包含完整metadata")
            self.initialized = True
            return True

        except Exception as e:
            logger.warning(f"迁移JSON缓存失败: {e}")
            return False

    def _validate_cache(self, cached_data: Dict[str, Any], data_path: str) -> bool:
        """验证缓存有效性"""
        try:
            # {{ AURA-X: Fix - 增强缓存验证，检查版本和metadata. Approval: 寸止(ID:1738230402). }}
            # 检查必需字段
            required_fields = ['examples', 'embeddings', 'dataset_path']
            if not all(field in cached_data for field in required_fields):
                return False

            # 检查版本和metadata
            cache_version = cached_data.get('version', '1.0')
            if cache_version < '1.1':
                logger.info(f"缓存版本过旧: {cache_version}，需要重新生成")
                return False

            # 检查metadata字段
            if 'metadata' not in cached_data:
                logger.info("缓存缺少metadata字段，需要重新生成")
                return False

            # 检查数据集是否已更改
            if cached_data['dataset_path'] != data_path:
                return False

            # 检查数据集文件哈希（如果存在）
            if 'dataset_hash' in cached_data:
                current_hash = self._get_dataset_hash(data_path)
                if cached_data['dataset_hash'] != current_hash:
                    logger.info("数据集文件已更改，缓存失效")
                    return False

            # 检查数据一致性
            examples = cached_data['examples']
            embeddings = cached_data['embeddings']
            metadata = cached_data['metadata']

            if len(examples) != len(embeddings) or len(examples) != len(metadata):
                logger.info(f"数据长度不匹配: examples={len(examples)}, embeddings={len(embeddings)}, metadata={len(metadata)}")
                return False

            return True

        except Exception:
            return False

    def _get_dataset_hash(self, data_path: str) -> str:
        """获取数据集文件哈希"""
        try:
            with open(data_path, 'rb') as f:
                content = f.read()
            return hashlib.md5(content).hexdigest()
        except Exception:
            return ""

    async def _generate_embeddings_in_batches(self, texts: List[str], batch_size: int = 100, max_concurrent: int = 10) -> List[List[float]]:
        """🚀 并发批量生成嵌入向量，大幅提升效率"""
        total_batches = (len(texts) + batch_size - 1) // batch_size

        logger.info(f"🚀 开始并发生成嵌入向量: {len(texts)} 个文本, {total_batches} 个批次, 最大并发: {max_concurrent} (优化版)")

        # 创建批次任务
        batch_tasks = []
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_num = i // batch_size + 1
            batch_tasks.append(self._process_single_batch(batch_texts, batch_num, total_batches))

        # 使用信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_with_semaphore(task):
            async with semaphore:
                return await task

        # 并发执行所有批次
        try:
            batch_results = await asyncio.gather(*[process_with_semaphore(task) for task in batch_tasks])

            # 合并结果
            all_embeddings = []
            for batch_embeddings in batch_results:
                if batch_embeddings:
                    all_embeddings.extend(batch_embeddings)
                else:
                    logger.error("某个批次嵌入生成失败")
                    return []

            logger.info(f"✅ 并发嵌入生成完成: {len(all_embeddings)} 个向量")
            return all_embeddings

        except Exception as e:
            logger.error(f"❌ 并发嵌入生成失败: {e}")
            return []

    async def _process_single_batch(self, batch_texts: List[str], batch_num: int, total_batches: int) -> List[List[float]]:
        """处理单个批次的嵌入生成"""
        try:
            logger.info(f"🔄 处理批次 {batch_num}/{total_batches}: {len(batch_texts)} 个文本")
            batch_embeddings = await self.model_service.get_embeddings_async(batch_texts)
            if batch_embeddings:
                logger.info(f"✅ 批次 {batch_num} 完成")
                return batch_embeddings
            else:
                logger.error(f"❌ 批次 {batch_num} 嵌入生成失败")
                return []
        except Exception as e:
            logger.error(f"❌ 批次 {batch_num} 处理失败: {e}")
            return []

    async def _generate_and_cache_vectors(self, data_path: str, pkl_cache_file: str) -> bool:
        """生成向量并保存到pkl缓存"""
        try:
            # 在线程池中加载数据集，避免阻塞事件循环
            def _load_dataset():
                with open(data_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                examples = await asyncio.get_event_loop().run_in_executor(executor, _load_dataset)

            # 批量生成语义向量（避免API超时）
            texts = [example.get('text', '') for example in examples]
            embeddings = await self._generate_embeddings_in_batches(texts, batch_size=50)

            if not embeddings or len(embeddings) != len(examples):
                logger.error(f"嵌入生成失败: 预期 {len(examples)}, 实际 {len(embeddings) if embeddings else 0}")
                return False

            # 计算维度特征并添加到metadata
            logger.info("🧠 开始计算训练数据的维度特征...")
            metadata = []
            for i, example in enumerate(examples):
                text = example.get('text', '')

                # 转换label格式为entities格式
                entities = []
                labels = example.get('label', {})
                for entity_type, entity_list in labels.items():
                    for entity_text in entity_list:
                        # 在文本中查找实体位置
                        start_pos = text.find(entity_text)
                        if start_pos != -1:
                            entities.append({
                                'text': entity_text,
                                'label': entity_type,
                                'start': start_pos,
                                'end': start_pos + len(entity_text)
                            })

                # 计算维度特征
                dimensions = self.dimension_calculator.calculate_all_dimensions(text, entities)
                metadata.append(dimensions)

                if (i + 1) % 100 == 0:
                    logger.info(f"🧠 已计算 {i + 1}/{len(examples)} 个样本的维度特征")

            logger.info(f"✅ 维度特征计算完成: {len(metadata)} 个样本")

            # 添加到向量存储
            self.vector_store.add_examples(examples, embeddings, metadata)

            # 保存到pkl缓存
            # {{ AURA-X: Fix - 添加metadata字段到缓存数据. Approval: 寸止(ID:1738230402). }}
            cache_data = {
                'examples': examples,
                'embeddings': embeddings,
                'metadata': metadata,  # 关键修复：保存维度特征metadata
                'dataset_path': data_path,
                'created_at': time.time(),
                'version': '1.1',  # 版本升级，标识包含metadata
                'dataset_hash': self._get_dataset_hash(data_path)
            }

            # 在线程池中保存缓存，避免阻塞事件循环
            def _save_cache():
                with open(pkl_cache_file, 'wb') as f:
                    pickle.dump(cache_data, f)

            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                await asyncio.get_event_loop().run_in_executor(executor, _save_cache)

            logger.info(f"向量存储初始化完成，生成并缓存 {len(examples)} 个示例")
            self.initialized = True
            return True

        except Exception as e:
            logger.error(f"生成向量缓存失败: {e}")
            return False

    async def initialize_vector_store(self, data_path: Optional[str] = None) -> bool:
        """🔍 KISS原则：检测有无向量库，有则继续，没有就生成"""
        try:
            # 获取数据集路径
            if data_path is None:
                data_path = get_current_dataset_path()

            if not os.path.exists(data_path):
                logger.warning(f"数据集文件不存在: {data_path}")
                return False

            # 简单的缓存检测
            from config import get_dataset_cache_dir
            cache_dir = get_dataset_cache_dir()

            file_name = os.path.basename(data_path).replace('.json', '')
            pkl_cache_file = os.path.join(cache_dir, f"{file_name}_vectors.pkl")

            # 检测向量库是否存在
            if os.path.exists(pkl_cache_file):
                logger.info("🔍 检测到现有向量库，正在加载...")
                if await self._load_pkl_cache(pkl_cache_file, data_path):
                    logger.info("✅ 向量库加载成功")
                    return True
                else:
                    logger.warning("⚠️ 向量库加载失败，重新生成...")

            # 向量库不存在或损坏，生成新的
            logger.info("🚀 向量库不存在，开始生成...")
            success = await self._generate_and_cache_vectors(data_path, pkl_cache_file)
            if success:
                logger.info("✅ 向量库生成完成")
            return success

        except Exception as e:
            logger.error(f"向量库初始化失败: {e}")
            return False




    async def _generate_query_embedding(self, query: str) -> List[float]:
        """生成查询嵌入 - 使用批处理器优化"""
        try:
            # {{ AURA-X: Fix - 添加调试信息. Approval: 寸止(ID:1754055919). }}
            logger.info(f"🔍 开始生成查询嵌入: {query[:50]}...")
            request_id = f"query_{hash(query)}_{time.time()}"
            logger.info(f"🔍 请求ID: {request_id}")
            embedding = await self.batch_processor.add_request(request_id, query)
            logger.info(f"🔍 嵌入生成完成: 长度={len(embedding) if embedding else 0}")
            if not embedding:
                logger.warning(f"🚨 查询嵌入为空: query='{query[:50]}...', request_id={request_id}")
            return embedding if embedding else []
        except Exception as e:
            logger.error(f"🚨 生成查询嵌入失败: query='{query[:50]}...', 错误: {e}")
            import traceback
            logger.error(f"🚨 查询嵌入失败详细堆栈: {traceback.format_exc()}")
            return []

    async def retrieve_with_dimensions(
        self,
        query_text: str,
        query_dimensions: Optional[Dict[str, Any]] = None,
        k: int = 3
    ) -> List[Dict[str, Any]]:
        """
        多维度检索方法 - 结合文本相似度和维度相似度

        Args:
            query_text: 查询文本描述
            query_dimensions: 多维度特征
            k: 需要的示例数量

        Returns:
            List[Dict]: k个示例
        """
        # 检查多维度配置
        multi_config = CONFIG.get('multi_dimensional_config', {})
        if not multi_config.get('enabled', False):
            logger.info("🔮 多维度检索未启用，使用纯文本检索")
            return await self.simple_retrieve(query_text, k)

        # 如果没有提供查询维度，从文本估算
        if not query_dimensions:
            logger.info("🧠 从查询文本估算维度特征")
            query_dimensions = self.dimension_calculator.estimate_dimensions_from_text(query_text)

        logger.info(f"🔮 多维度检索: {query_dimensions}")

        # 获取更多候选结果用于重排序
        candidate_count = max(k * 4, 20)  # 获取4倍候选数量
        candidates = await self.simple_retrieve(query_text, candidate_count)

        if not candidates:
            return []

        # {{ AURA-X: Extend - 使用RRF融合进行多维度检索. Approval: 寸止(ID:1738230404). }}
        # 准备多维度搜索
        text_ranking = [(candidate.get('index', -1), candidate.get('score', 0.0)) for candidate in candidates]
        dimension_rankings = self._parallel_dimension_search(query_dimensions, candidate_count)

        # 合并所有排名列表
        all_rankings = [text_ranking] + dimension_rankings

        # 计算RRF分数
        rrf_scores = self._calculate_rrf_score(all_rankings)

        # 重新评分候选样本
        scored_candidates = []
        weights = multi_config.get('dimension_weights', {})

        for candidate in candidates:
            candidate_idx = candidate.get('index', -1)

            # 获取RRF分数
            rrf_score = rrf_scores.get(candidate_idx, 0.0)

            # 获取文本相似度
            text_similarity = candidate.get('score', 0.0)

            # 计算最终分数（RRF + 文本相似度的加权组合）
            final_score = (
                weights.get('text_similarity', 0.6) * text_similarity +
                weights.get('rrf_fusion', 0.4) * rrf_score
            )

            candidate['multi_dim_score'] = final_score
            candidate['rrf_score'] = rrf_score

            # 添加维度相似度信息（用于调试）
            if candidate_idx >= 0 and candidate_idx < len(self.vector_store.metadata):
                candidate_dimensions = self.vector_store.metadata[candidate_idx]
                if candidate_dimensions and isinstance(candidate_dimensions, dict):
                    dim_similarity = self._calculate_dimension_similarity(query_dimensions, candidate_dimensions)
                    candidate['dimension_similarity'] = dim_similarity
                    logger.debug(f"🔮 样本{candidate_idx}: 文本={text_similarity:.3f}, RRF={rrf_score:.3f}, 最终={final_score:.3f}")
                else:
                    logger.debug(f"⚠️ 样本{candidate_idx}: metadata为空，RRF={rrf_score:.3f}")

            scored_candidates.append(candidate)

        # 按最终评分重新排序
        scored_candidates.sort(key=lambda x: x['multi_dim_score'], reverse=True)

        # 返回前k个结果
        result = scored_candidates[:k]

        # {{ AURA-X: Fix - 增强日志信息，显示多维度检索效果. Approval: 寸止(ID:1738230402). }}
        valid_metadata_count = sum(1 for c in scored_candidates if c.get('dimension_similarity'))
        logger.info(f"🎯 多维度检索完成: 返回{len(result)}个示例，{valid_metadata_count}/{len(scored_candidates)}个样本有有效metadata")

        if valid_metadata_count == 0:
            logger.warning("⚠️ 所有样本的metadata都为空，多维度检索退化为纯文本检索")

        return result

    async def retrieve_with_needs(self, needs: Dict[str, str], k: int = 3) -> List[Dict[str, Any]]:
        """
        {{ AURA-X: Add - 基于自然语言需求描述的多维度检索. Approval: 寸止(ID:1738230404). }}
        基于自然语言需求描述的检索方法

        Args:
            needs: 四个维度的自然语言需求描述
            k: 需要的示例数量

        Returns:
            List[Dict]: k个示例
        """
        try:
            logger.info(f"🔮 基于需求描述的检索: {needs}")

            # 验证和预处理需求
            valid_needs = self._validate_needs(needs)
            if not valid_needs:
                return await self.simple_retrieve("general NER examples", k)

            # 获取初始候选结果
            candidates = await self._get_initial_candidates(valid_needs, k)
            if not candidates:
                return []

            # 生成多维度排名
            rankings = await self._generate_multi_dimensional_rankings(valid_needs, candidates)

            # 融合排名并返回结果
            return self._fuse_rankings_and_score(rankings, candidates, k)

        except Exception as e:
            logger.error(f"🚨 基于需求的检索失败: {e}")
            return await self.simple_retrieve("general NER examples", k)

    def _validate_needs(self, needs: Dict[str, str]) -> Dict[str, str]:
        """验证和过滤有效的需求描述"""
        valid_needs = {key: value for key, value in needs.items() if value and value.strip()}
        if not valid_needs:
            logger.warning("⚠️ 没有有效的需求描述，回退到简单检索")
        return valid_needs

    async def _get_initial_candidates(self, valid_needs: Dict[str, str], k: int) -> List[Dict[str, Any]]:
        """基于语义需求获取初始候选结果"""
        candidate_count = max(k * 6, 30)

        semantic_need = valid_needs.get("semantic_need", "")
        if semantic_need:
            candidates = await self.simple_retrieve(semantic_need, candidate_count)
        else:
            first_need = next(iter(valid_needs.values()))
            candidates = await self.simple_retrieve(first_need, candidate_count)

        if not candidates:
            logger.warning("⚠️ 初始检索未返回候选结果")

        return candidates

    async def _generate_multi_dimensional_rankings(self, valid_needs: Dict[str, str], candidates: List[Dict[str, Any]]) -> List[List[tuple]]:
        """为每个需求维度生成排名列表"""
        rankings = []

        # 语义维度排名（基于初始检索结果）
        semantic_ranking = [(candidate.get('index', -1), candidate.get('score', 0.0)) for candidate in candidates]
        rankings.append(semantic_ranking)

        # 其他维度的语义匹配排名
        for need_type, need_text in valid_needs.items():
            if need_type != "semantic_need" and need_text.strip():
                ranking = await self._generate_need_ranking(need_type, need_text, candidates)
                if ranking:
                    rankings.append(ranking)

        return rankings

    async def _generate_need_ranking(self, need_type: str, need_text: str, candidates: List[Dict[str, Any]]) -> List[tuple]:
        """
        {{ AURA-X: Enhance - 基于描述字段的语义匹配排名. Approval: 寸止(ID:1738230404). }}
        为单个需求维度生成排名，使用对应的描述字段进行语义匹配
        """
        try:
            # 映射需求类型到描述字段
            description_field_map = {
                'semantic_need': 'semantic_description',
                'syntactic_need': 'syntactic_description',
                'entity_need': 'entity_description',
                'style_need': 'style_description'
            }

            target_field = description_field_map.get(need_type)
            if not target_field:
                logger.warning(f"⚠️ 未知的需求类型: {need_type}")
                return []

            # 生成需求文本的embedding
            need_embedding = await self._generate_query_embedding(need_text)
            if need_embedding is None:
                return []

            need_ranking = []
            for candidate in candidates:
                candidate_idx = candidate.get('index', -1)

                # 获取候选样本的metadata
                if hasattr(self.vector_store, 'metadata') and self.vector_store.metadata:
                    if 0 <= candidate_idx < len(self.vector_store.metadata):
                        candidate_metadata = self.vector_store.metadata[candidate_idx]
                        target_description = candidate_metadata.get(target_field, "") if isinstance(candidate_metadata, dict) else ""
                    else:
                        target_description = ""

                    if target_description:
                        # 计算需求文本与目标描述的语义相似度
                        try:
                            desc_embedding = await self._generate_query_embedding(target_description)
                            if desc_embedding is not None:
                                # 计算余弦相似度
                                import numpy as np
                                similarity = np.dot(need_embedding, desc_embedding) / (
                                    np.linalg.norm(need_embedding) * np.linalg.norm(desc_embedding)
                                )
                                need_ranking.append((candidate_idx, float(similarity)))
                            else:
                                # 回退到原始分数
                                similarity = candidate.get('score', 0.0) * 0.5
                                need_ranking.append((candidate_idx, similarity))
                        except Exception as e:
                            logger.debug(f"计算语义相似度失败: {e}")
                            similarity = candidate.get('score', 0.0) * 0.5
                            need_ranking.append((candidate_idx, similarity))
                    else:
                        # 没有描述字段，使用降权的原始分数
                        similarity = candidate.get('score', 0.0) * 0.3
                        need_ranking.append((candidate_idx, similarity))
                else:
                    # 没有metadata，使用降权的原始分数
                    similarity = candidate.get('score', 0.0) * 0.3
                    need_ranking.append((candidate_idx, similarity))

            # 按相似度排序
            need_ranking.sort(key=lambda x: x[1], reverse=True)
            return need_ranking

        except Exception as e:
            logger.warning(f"⚠️ 处理{need_type}需求时出错: {e}")
            return []

    def _fuse_rankings_and_score(self, rankings: List[List[tuple]], candidates: List[Dict[str, Any]], k: int) -> List[Dict[str, Any]]:
        """
        {{ AURA-X: Enhance - 使用配置权重的融合排名. Approval: 寸止(ID:1738230404). }}
        融合多个排名并计算最终分数
        """
        if len(rankings) > 1:
            # 从配置获取权重
            needs_config = CONFIG.get('needs_based_retrieval_config', {})
            dimension_weights = needs_config.get('dimension_weights', {})

            # 构建权重列表：第一个是语义权重，后续是其他维度权重
            weights = [dimension_weights.get('semantic_need', 1.0)]
            for _ in range(len(rankings) - 1):
                # 为其他维度使用平均权重
                other_weights = [
                    dimension_weights.get('syntactic_need', 0.8),
                    dimension_weights.get('entity_need', 0.9),
                    dimension_weights.get('style_need', 0.7)
                ]
                avg_weight = sum(other_weights) / len(other_weights)
                weights.append(avg_weight)

            # 获取RRF配置
            rrf_k = needs_config.get('rrf_config', {}).get('k_constant', 60)

            # 计算加权RRF分数
            rrf_scores = self._calculate_rrf_score(rankings, k=rrf_k, weights=weights)
            scored_candidates = self._apply_rrf_scores(candidates, rrf_scores)
            scored_candidates.sort(key=lambda x: x.get('final_score', 0.0), reverse=True)
        else:
            scored_candidates = candidates

        result = scored_candidates[:k]
        logger.info(f"🎯 基于需求的检索完成: 返回{len(result)}个示例，使用了{len(rankings)}个维度排名")
        return result

    def _apply_rrf_scores(self, candidates: List[Dict[str, Any]], rrf_scores: Dict[int, float]) -> List[Dict[str, Any]]:
        """应用RRF分数到候选样本"""
        scored_candidates = []
        for candidate in candidates:
            candidate_idx = candidate.get('index', -1)
            rrf_score = rrf_scores.get(candidate_idx, 0.0)
            final_score = candidate.get('score', 0.0) * 0.3 + rrf_score * 0.7

            scored_candidates.append({
                **candidate,
                'final_score': final_score,
                'rrf_score': rrf_score,
                'needs_match': True
            })

        return scored_candidates

    def _calculate_dimension_similarity(self, query_dims: Dict[str, Any], candidate_dims: Dict[str, Any]) -> Dict[str, float]:
        """
        计算多维度相似度

        Args:
            query_dims: 查询的维度特征
            candidate_dims: 候选样本的维度特征

        Returns:
            Dict[str, float]: 各维度的相似度评分
        """
        similarities = {}

        # {{ AURA-X: Extend - 扩展维度相似度计算，支持新维度. Approval: 寸止(ID:1738230404). }}
        # 数值维度相似度计算
        numerical_dims = {
            'entity_density': 1.0,      # sigma=1.0
            'boundary_ambiguity': 0.3,  # sigma=0.3
            'dependency_depth': 2.0,    # sigma=2.0
            'formality_level': 0.2,     # sigma=0.2
            'information_density': 0.3   # sigma=0.3
        }

        for dim, sigma in numerical_dims.items():
            if dim in query_dims and dim in candidate_dims:
                diff = abs(float(query_dims[dim]) - float(candidate_dims[dim]))
                # 使用高斯函数计算相似度
                similarities[dim] = np.exp(-(diff ** 2) / (2 * sigma ** 2))
            else:
                similarities[dim] = 0.0

        # 类别维度相似度计算
        categorical_dims = ['rhetorical_role', 'instruction_complexity']

        for dim in categorical_dims:
            if dim in query_dims and dim in candidate_dims:
                # 完全匹配得1分，不匹配得0分
                similarities[dim] = 1.0 if query_dims[dim] == candidate_dims[dim] else 0.0
            else:
                similarities[dim] = 0.0

        return similarities

    def _calculate_rrf_score(self, rankings: List[List[tuple]], k: int = 60, weights: Optional[List[float]] = None) -> Dict[int, float]:
        """
        {{ AURA-X: Enhance - 增强RRF融合算法，支持权重和自适应参数. Approval: 寸止(ID:1738230404). }}
        计算加权倒数排名融合(Weighted RRF)分数

        Args:
            rankings: 多个排名列表，每个列表包含(doc_id, score)元组
            k: RRF常数，通常为60
            weights: 各排名列表的权重，如果为None则使用等权重

        Returns:
            Dict[int, float]: 文档ID到RRF分数的映射
        """
        if not rankings:
            return {}

        # 设置默认权重
        if weights is None:
            weights = [1.0] * len(rankings)
        elif len(weights) != len(rankings):
            logger.warning(f"⚠️ 权重数量({len(weights)})与排名列表数量({len(rankings)})不匹配，使用等权重")
            weights = [1.0] * len(rankings)

        # 归一化权重
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]
        else:
            weights = [1.0 / len(rankings)] * len(rankings)

        rrf_scores = {}
        ranking_stats = []

        # 计算每个排名列表的统计信息，用于自适应调整
        for i, ranking in enumerate(rankings):
            if ranking:
                scores = [score for _, score in ranking]
                stats = {
                    'count': len(ranking),
                    'max_score': max(scores) if scores else 0.0,
                    'min_score': min(scores) if scores else 0.0,
                    'score_range': max(scores) - min(scores) if scores else 0.0
                }
                ranking_stats.append(stats)
            else:
                ranking_stats.append({'count': 0, 'max_score': 0.0, 'min_score': 0.0, 'score_range': 0.0})

        # 计算加权RRF分数
        for i, ranking in enumerate(rankings):
            weight = weights[i]
            stats = ranking_stats[i]

            # 自适应调整k值：根据排名列表的质量动态调整
            adaptive_k = self._calculate_adaptive_k(k, stats)

            for rank, (doc_id, original_score) in enumerate(ranking):
                if doc_id not in rrf_scores:
                    rrf_scores[doc_id] = 0.0

                # 增强的RRF公式：结合权重和自适应k值
                rrf_contribution = weight * (1.0 / (adaptive_k + rank + 1))

                # 可选：结合原始分数的信息
                score_boost = self._calculate_score_boost(original_score, stats)
                final_contribution = rrf_contribution * (1.0 + score_boost)

                rrf_scores[doc_id] += final_contribution

        # 归一化最终分数
        if rrf_scores:
            max_score = max(rrf_scores.values())
            if max_score > 0:
                rrf_scores = {doc_id: score / max_score for doc_id, score in rrf_scores.items()}

        # 记录融合统计信息
        self._log_rrf_fusion_stats(rankings, rrf_scores, weights)

        logger.debug(f"🔮 RRF融合完成: {len(rankings)}个排名列表，权重={weights}，生成{len(rrf_scores)}个分数")
        return rrf_scores

    def _calculate_adaptive_k(self, base_k: int, stats: Dict[str, float]) -> float:
        """
        {{ AURA-X: Add - 自适应k值计算. Approval: 寸止(ID:1738230404). }}
        根据排名列表的统计信息自适应调整k值
        """
        # 基于排名列表的质量调整k值
        # 高质量排名（分数范围大）使用较小的k值，增加区分度
        # 低质量排名（分数范围小）使用较大的k值，减少噪声影响

        score_range = stats.get('score_range', 0.0)
        count = stats.get('count', 0)

        if count == 0:
            return base_k

        # 质量因子：分数范围越大，质量越高
        quality_factor = min(score_range * 2.0, 1.0)  # 限制在[0, 1]

        # 自适应调整：高质量用较小k，低质量用较大k
        adaptive_k = base_k * (1.0 - quality_factor * 0.3)  # 最多减少30%

        return max(adaptive_k, base_k * 0.5)  # 确保k不会太小

    def _calculate_score_boost(self, original_score: float, stats: Dict[str, float]) -> float:
        """
        {{ AURA-X: Add - 原始分数增强因子计算. Approval: 寸止(ID:1738230404). }}
        基于原始分数在其排名列表中的相对位置计算增强因子
        """
        min_score = stats.get('min_score', 0.0)
        score_range = stats.get('score_range', 1.0)

        if score_range == 0:
            return 0.0

        # 计算相对分数位置 [0, 1]
        relative_score = (original_score - min_score) / score_range

        # 轻微的增强因子，避免过度影响RRF的排名逻辑
        boost = relative_score * 0.1  # 最多10%的增强

        return boost

    def _log_rrf_fusion_stats(self, rankings: List[List[tuple]], rrf_scores: Dict[int, float], weights: List[float]):
        """
        {{ AURA-X: Add - RRF融合统计日志. Approval: 寸止(ID:1738230404). }}
        记录RRF融合的统计信息，用于调试和性能分析
        """
        if not logger.isEnabledFor(logging.INFO):
            return

        # 统计每个排名列表的贡献
        total_docs = len(rrf_scores)
        ranking_contributions = []

        for i, ranking in enumerate(rankings):
            weight = weights[i] if i < len(weights) else 1.0
            unique_docs = len(set(doc_id for doc_id, _ in ranking))
            avg_rank = sum(rank for rank, _ in enumerate(ranking)) / len(ranking) if ranking else 0

            ranking_contributions.append({
                'index': i,
                'weight': weight,
                'docs': len(ranking),
                'unique_docs': unique_docs,
                'avg_rank': avg_rank
            })

        # 分析分数分布
        if rrf_scores:
            scores = list(rrf_scores.values())
            score_stats = {
                'min': min(scores),
                'max': max(scores),
                'avg': sum(scores) / len(scores),
                'std': (sum((s - sum(scores) / len(scores)) ** 2 for s in scores) / len(scores)) ** 0.5
            }
        else:
            score_stats = {'min': 0, 'max': 0, 'avg': 0, 'std': 0}

        logger.info(f"🔮 RRF融合统计: {len(rankings)}个排名列表 → {total_docs}个文档")
        logger.info(f"📊 分数分布: min={score_stats['min']:.3f}, max={score_stats['max']:.3f}, "
                   f"avg={score_stats['avg']:.3f}, std={score_stats['std']:.3f}")

        for contrib in ranking_contributions:
            logger.info(f"📈 排名{contrib['index']}: 权重={contrib['weight']:.2f}, "
                       f"文档={contrib['docs']}, 唯一={contrib['unique_docs']}, "
                       f"平均排名={contrib['avg_rank']:.1f}")

    def _parallel_dimension_search(self, query_dims: Dict[str, Any], k: int) -> List[List[tuple]]:
        """
        【优化版】并行多维度搜索 - 使用NumPy向量化计算
        """
        if not self.vector_store.metadata:
            logger.warning("⚠️ 向量库metadata为空，无法进行多维度搜索")
            return []

        # 1. 数据准备：将metadata列表转换为NumPy数组，只做一次！
        all_metadata = self.vector_store.metadata
        num_candidates = len(all_metadata)

        # 从维度注册表获取维度配置
        numerical_dims = {}
        categorical_dims = {}

        # 如果维度注册表可用，使用注册表配置
        if DIMENSION_REGISTRY:
            for dim_name, dim_config in DIMENSION_REGISTRY.items():
                if dim_config['type'] == 'numerical':
                    numerical_dims[dim_name] = dim_config['similarity_params']
                elif dim_config['type'] == 'categorical':
                    categorical_dims[dim_name] = dim_config['similarity_params']
        else:
            # 回退到硬编码配置
            numerical_dims = {
                'entity_density': {'sigma': 1.0, 'weight': 0.2},
                'boundary_ambiguity': {'sigma': 0.3, 'weight': 0.2},
                'dependency_depth': {'sigma': 2.0, 'weight': 0.2},
                'formality_level': {'sigma': 0.2, 'weight': 0.1},
                'information_density': {'sigma': 0.3, 'weight': 0.1}
            }
            categorical_dims = {
                'rhetorical_role': {'weight': 0.1},
                'instruction_complexity': {'weight': 0.1}
            }

        # 初始化总分数组
        total_scores = np.zeros(num_candidates)
        total_weights = np.zeros(num_candidates)

        # 2. 高效计算：向量化处理
        # 处理数值型维度
        for dim, params in numerical_dims.items():
            if dim in query_dims:
                # 从所有候选中提取该维度的值，形成一个向量
                candidate_values = np.array([meta.get(dim, 0.0) for meta in all_metadata], dtype=np.float32)
                query_value = float(query_dims[dim])

                # NumPy向量化计算高斯相似度
                diff = np.abs(candidate_values - query_value)
                similarity_scores = np.exp(-(diff ** 2) / (2 * params['sigma'] ** 2))

                # 累加加权分数
                total_scores += similarity_scores * params['weight']
                total_weights += params['weight']

        # 处理类别型维度
        for dim, params in categorical_dims.items():
            if dim in query_dims:
                # 提取所有候选的类别值
                candidate_values = np.array([meta.get(dim, '') for meta in all_metadata])
                query_value = query_dims[dim]

                # NumPy向量化比较，得到一个布尔数组，然后转为0/1
                similarity_scores = (candidate_values == query_value).astype(np.float32)

                # 累加加权分数
                total_scores += similarity_scores * params['weight']
                total_weights += params['weight']

        # 3. 计算最终的加权平均分，避免除以零
        final_scores = np.divide(total_scores, total_weights, out=np.zeros_like(total_scores), where=total_weights!=0)

        # 4. 排序并返回结果
        # 使用np.argsort获取排序后的索引，[-k:]取最大的k个，[::-1]反转顺序
        top_k_indices = np.argsort(final_scores)[-k:][::-1]

        rankings = [
            (int(idx), float(final_scores[idx])) for idx in top_k_indices
        ]

        # 因为我们只有一个综合排名，所以把它包在列表里返回
        return [rankings]

    async def simple_retrieve(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        高性能检索方法 - 使用FAISS+批处理优化

        Args:
            description: LLM生成的检索描述
            k: 需要的示例数量

        Returns:
            List[Dict]: k个示例
        """
        try:
            if not self.initialized:
                logger.warning("⚠️ 向量存储未初始化")
                return []

            logger.info(f"🔍 高性能检索: {description[:50]}..., k={k}")

            # 生成查询嵌入（使用批处理器）
            query_embedding = await self._generate_query_embedding(description)
            if not query_embedding:
                logger.warning("查询嵌入生成失败")
                return []

            # 使用FAISS进行高速检索
            if isinstance(self.vector_store, FAISSVectorStore) and self.vector_store.initialized:
                # {{ AURA-X: Fix - 添加FAISS检索调试信息. Approval: 寸止(ID:1754055919). }}
                logger.info(f"🔍 开始FAISS检索: k={k}")
                search_results = await self.vector_store.search(query_embedding, top_k=k)
                logger.info(f"🔍 FAISS检索完成: 结果数={len(search_results)}")

                results = []
                for idx, score in search_results:
                    if idx < len(self.vector_store.examples):
                        example = self.vector_store.examples[idx]
                        results.append({
                            'text': example.get('text', ''),
                            'label': example.get('label', {}),
                            'similarity_score': float(score),
                            'index': idx  # 添加索引字段，用于多维度检索
                        })

                logger.info(f"✅ FAISS检索完成: 返回 {len(results)} 个示例")
                return results
            else:
                logger.warning("⚠️ FAISS向量存储未初始化")
                return []

        except Exception as e:
            logger.error(f"检索失败: {e}")
            return []




# 全局单例
example_retriever = ExampleRetriever()
