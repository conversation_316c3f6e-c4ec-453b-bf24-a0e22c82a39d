from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from enum import Enum

# ====== 🚀 元认知智能体Function Calling工具 ======

class DomainType(str, Enum):
    """领域类型枚举"""
    NEWS = "news"
    MEDICAL = "medical"
    FINANCIAL = "financial"
    ACADEMIC = "academic"
    SOCIAL = "social"
    GENERAL = "general"

class RetrieveNERExamplesTool(BaseModel):
    """🧠 多维度NER示例检索工具 - 元认知驱动的ICL示例检索"""

    # 基础描述（保持兼容）
    description: str = Field(
        description="简洁描述文本的关键特征，重点关注：文本类型(新闻/对话/学术等)、领域(医疗/金融/通用等)、语言风格(正式/口语等)、实体复杂度。20-30个词以内。"
    )

    # {{ AURA-X: Extend - 基于用户方案扩展多维度特征. Approval: 寸止(ID:**********). }}
    # 核心维度（现有，保持兼容）
    entity_density: Optional[float] = Field(
        description="实体密度：预估文本中实体数量与句子数量的比值，范围0-10。例如：低密度0-1，中密度2-3，高密度4+",
        default=None,
        ge=0.0,
        le=10.0
    )

    boundary_ambiguity: Optional[float] = Field(
        description="实体边界模糊度：预估实体边界不清晰的程度，0-1范围。0=边界清晰(如'Apple')，1=高度模糊(如'New York-based company')",
        default=None,
        ge=0.0,
        le=1.0
    )

    dependency_depth: Optional[int] = Field(
        description="语法依赖深度：预估句子中依赖关系的最大层次深度，1-10范围。简单句=1-2，复杂句=3-5，极复杂=6+",
        default=None,
        ge=1,
        le=10
    )

    # 新增维度（基于用户方案）
    rhetorical_role: Optional[str] = Field(
        description="修辞角色：文本的修辞功能，如'陈述事实'、'提出观点'、'举例说明'、'进行反驳'",
        default=None
    )

    formality_level: Optional[float] = Field(
        description="正式度：语言的正式程度，0-1范围。0=非正式/口语化，1=高度正式/学术化",
        default=None,
        ge=0.0,
        le=1.0
    )

    information_density: Optional[float] = Field(
        description="信息密度：文本的信息量/惊奇度，0-1范围。0=常见/可预测，1=罕见/高信息量",
        default=None,
        ge=0.0,
        le=1.0
    )

    instruction_complexity: Optional[str] = Field(
        description="指令复杂度：任务的复杂程度，'低'、'中'、'高'",
        default=None
    )

    # 扩展维度预留
    additional_features: Optional[Dict[str, Any]] = Field(
        description="额外的维度特征，用于未来扩展新的分析维度",
        default=None
    )

    k: int = Field(
        description="需要检索的示例数量，推荐2-3个以获得更好的覆盖度",
        default=3,
        ge=1,
        le=5
    )


class SpecifyExampleNeedsTool(BaseModel):
    """🧠 元认知驱动的NER示例需求描述工具 - 基于自然语言需求的智能检索"""

    semantic_need: str = Field(
        description="""语义需求：详细描述你需要什么主题/内容的示例来帮助理解当前文本中的实体。
        思考要点：
        - 这段文本的主题领域是什么？(如：科技、医疗、金融、法律等)
        - 什么样的内容示例能帮你更好地识别其中的实体？
        - 是否需要特定行业或场景的示例？

        示例描述：
        - '一篇关于科技公司财报的商业新闻报道'
        - '医疗诊断报告中包含症状和药物名称的文本'
        - '法律文件中涉及公司并购的条款描述'
        - '社交媒体上讨论体育赛事的用户评论'"""
    )

    syntactic_need: str = Field(
        description="""句法需求：详细描述你需要什么句法结构的示例来帮助解析当前文本。
        思考要点：
        - 这段文本的句子结构复杂度如何？
        - 是否包含长句、从句、并列结构？
        - 什么样的句法示例能帮你更好地定位实体边界？

        示例描述：
        - '包含多个并列从句和定语从句的复杂长句'
        - '简短直接的陈述句，结构清晰明了'
        - '包含大量专业术语和缩写的技术性句子'
        - '口语化表达，包含省略和倒装的非正式句子'"""
    )

    entity_need: str = Field(
        description="""实体需求：详细描述你需要什么实体特征的示例来指导当前文本的实体识别。
        思考要点：
        - 这段文本中的实体类型有哪些？(人名、地名、机构名等)
        - 实体边界是否清晰？是否存在嵌套或模糊情况？
        - 实体密度如何？是否需要处理密集的实体分布？

        示例描述：
        - '实体密集且边界模糊的文本，特别是包含嵌套的组织名称'
        - '实体边界清晰的正式文档，主要包含人名和地名'
        - '包含大量缩写和代号的技术文档，实体识别具有挑战性'
        - '实体稀少但类型多样的叙述性文本'"""
    )

    style_need: str = Field(
        description="""风格需求：详细描述你需要什么语言风格的示例来匹配当前文本的表达方式。
        思考要点：
        - 这段文本的语言风格是正式还是非正式？
        - 是书面语还是口语化表达？
        - 是否有特定的行业术语或表达习惯？

        示例描述：
        - '正式的学术论文语言，用词严谨规范'
        - '非正式的社交媒体对话，包含网络用语和表情符号'
        - '新闻报道的客观叙述风格，语言简洁明了'
        - '技术文档的专业表达，包含大量专业术语'"""
    )

